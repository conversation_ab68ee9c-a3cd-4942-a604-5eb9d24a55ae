import { GameState } from '../enums/GameState';
import { IGameState } from '../interfaces/IGameState';
import { Game } from '../Game';

export class InitializingState implements IGameState {
    constructor(private _game: Game) { }

    enter(): void {
        console.log('游戏初始化中...');
        // 加载资源
        // 初始化游戏数据

        //初始化完成
        this._game.setInitialized();

        // 自动切换到游戏状态，方便测试
        setTimeout(() => {
            this._game.changeState(GameState.PLAYING);
        }, 500);
    }

    exit(): void {
        console.log('游戏初始化完成');
    }

    update(_deltaTime: number): void {
        // 初始化状态下不做任何更新，让 setTimeout 自动切换到 PLAYING 状态
        // 注意：之前这里会切换到 PAUSED 状态，与 setTimeout 切换到 PLAYING 冲突
    }


}



export class PlayingState implements IGameState {
    constructor(private _game: Game) { }

    enter(): void {
        console.log('开始游戏');
        this._game.clearGameTime();
        // 开始游戏逻辑
    }

    exit(): void {
        console.log('暂停游戏');
        // 暂停游戏逻辑
    }

    update(deltaTime: number): void {
        this._game.setGameTime(deltaTime);
        // 更新游戏逻辑
        this._game.gameManager.update(deltaTime);
    }


}

export class PausedState implements IGameState {
    constructor(private _game: Game) { }

    enter(): void {
        console.log('游戏待开始');
        // 显示暂停菜单
    }

    exit(): void {
        console.log('退出暂停');
        // 隐藏暂停菜单
    }

    update(_deltaTime: number): void {
        // 暂停状态下不更新游戏逻辑
    }


}
export class ResmueState implements IGameState {
    constructor(private _game: Game) { }

    enter(): void {
        console.log('继续游戏');
        // 显示暂停菜单
    }

    exit(): void {
        console.log('游戏开始');
        // 隐藏暂停菜单
    }

    update(deltaTime: number): void {
        // 继续游戏状态
        if (this._game.isInitialized) {
            this._game.changeState(GameState.PLAYING);
        }
    }
}
export class GameOverState implements IGameState {
    constructor(private _game: Game) { }

    enter(): void {
        console.log('游戏结束');
        // 显示游戏结束UI
    }

    exit(): void {
        console.log('退出游戏结束状态');
        // 隐藏游戏结束UI
    }

    update(_deltaTime: number): void {
        // 更新游戏结束动画等
        if (this._game.isInitialized) {
            this._game.changeState(GameState.PAUSED);
        }
    }

}

