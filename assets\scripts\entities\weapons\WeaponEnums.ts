// 武器类型枚举
export enum WeaponType {
    // 基础型武器
    MACHINE_GUN = 'machine_gun',       // 机枪塔
    FLAME_THROWER = 'flame_thrower',   // 火焰喷射器
    SNIPER = 'sniper',                 // 狙击炮台
    BOW = 'bow',                       // 普通弓箭
    CROSSBOW = 'crossbow',             // 弩箭塔
    CATAPULT = 'catapult',             // 投石机

  
}

// 武器等级枚举
export enum WeaponTier {
    COMMON = 'common',
    RARE = 'rare',
    EPIC = 'epic',
    LEGENDARY = 'legendary'
}

// 伤害类型枚举
export enum DamageType {
    PHYSICAL = 'physical',
    FIRE = 'fire',
    ELECTRIC = 'electric',
    POISON = 'poison'
}

// 状态效果枚举
export enum StatusEffect {
    BURNING = 'burning',
    POISONED = 'poisoned',
    STUNNED = 'stunned',
    SLOWED = 'slowed',
    FROZEN = 'frozen',
    MARKED = 'marked',
    NONE = 'none'
}

// 升级路线枚举
export enum UpgradePath {
    PATH_A = 'path_a',
    PATH_B = 'path_b',
    ULTIMATE = 'ultimate'
}

// 弹道类型枚举
export enum ProjectileTrajectory {
    STRAIGHT = 'straight',      // 直线
    PARABOLA = 'parabola',      // 抛物线
    FALLING = 'falling',       // 空中落下
    HOMING = 'homing',        // 追踪
    BOUNCE = 'bounce',        // 弹跳
    SPIRAL = 'spiral'         // 螺旋
} 