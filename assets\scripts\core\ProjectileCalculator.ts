import { PhysicsSystem } from 'cc';
import { Vector3, Vector3Util } from '../utils/Vector3';
import { VectorUtil } from '../utils/VectorUtil';

/** 用于弧度转角度 */
const rad2Deg = 180 / Math.PI;

/** 用于角度转弧度 */
const deg2Rad = Math.PI / 180;

/**
 * 抛射物计算器
 * 提供抛射物运动相关的数学计算功能
 */
export class ProjectileCalculator {
    /**
     * 重力加速度（垂直向下）
     */
    private static get gravity() {
        return Math.abs(PhysicsSystem.instance.gravity.y);
    }

    /**
     * 计算位移距离
     * @param fromPos 起始位置
     * @param toPos 目标位置
     * @returns 水平和垂直位移
     */
    public static calculateDisplacement(fromPos: Vector3, toPos: Vector3) {
        // 从起始点到目标点的方向矢量
        const directionVec = Vector3Util.create();
        Vector3Util.subtract(directionVec, toPos, fromPos);

        // 垂直位移
        const vertical = directionVec.y;

        // 水平位移（将方向矢量投影到水平面上，其长度就是水平位移的长度）
        const horizontalVec = VectorUtil.projectOnPlane(directionVec, Vector3Util.UP);
        const horizontal = Vector3Util.len(horizontalVec);

        return { horizontal, vertical };
    }

    /**
     * 计算水平距离耗时
     * @param x 水平位移
     * @param angle 初始角度
     * @param velocity 初始速度
     * @returns 时间
     */
    public static calculateTotalTime(x: number, angle: number, velocity: number) {
        // 初始角度（弧度制）
        const θ = angle * deg2Rad;

        // 时间
        // t = x / ( v * cos(θ) )
        const t = x / (velocity * Math.cos(θ));

        return t;
    }

    /**
     * 计算抛射物到达目标所需的时间
     * @param horizontal 水平距离
     * @param vertical 垂直距离（目标高度 - 发射点高度）
     * @param angle 初始角度（角度制）
     * @param velocity 初始速度
     * @param debug 是否输出调试信息
     * @returns 到达目标所需的时间，如果无法到达则返回 NaN
     */
    public static calculateTimeToTarget(horizontal: number, vertical: number, angle: number, velocity: number, debug: boolean = false): number {
        // 重力加速度（垂直向下）
        const g = ProjectileCalculator.gravity;
        // 初始角度（弧度制）
        const θ = angle * deg2Rad;

        // 水平速度分量
        const vx = velocity * Math.cos(θ);
        // 垂直速度分量
        const vy = velocity * Math.sin(θ);

        // 如果水平速度为0，无法到达目标
        if (Math.abs(vx) < 0.0001) {
            return NaN;
        }

        // 水平运动时间
        const t = horizontal / vx;

        // 垂直位移
        // y = vy * t - 0.5 * g * t^2
        const y = vy * t - 0.5 * g * Math.pow(t, 2);

        // 如果垂直位移与目标垂直距离相差太大，则认为无法到达目标
        if (Math.abs(y - vertical) > 0.1) {
            if (debug) {
                console.log("无法到达目标", {
                    angle,
                    angleRad: θ,
                    velocity,
                    horizontal,
                    vertical,
                    horizontalVelocity: vx,
                    verticalVelocity: vy,
                    time: t,
                    calculatedVertical: y,
                    difference: Math.abs(y - vertical)
                });
            }
            return NaN;
        }

        return t;
    }

    /**
     * 计算抛射物到达目标所需的时间（求解二次方程）
     * @param horizontal 水平距离
     * @param vertical 垂直距离（目标高度 - 发射点高度）
     * @param angle 初始角度（角度制）
     * @param velocity 初始速度
     * @returns 到达目标所需的时间，如果有两个解，返回较小的那个；如果无解，返回 NaN
     */
    public static calculateTimeToTargetPrecise(horizontal: number, vertical: number, angle: number, velocity: number): number {
        // 重力加速度（垂直向下）
        const g = ProjectileCalculator.gravity;
        // 初始角度（弧度制）
        const θ = angle * deg2Rad;

        // 水平速度分量
        const vx = velocity * Math.cos(θ);
        // 垂直速度分量
        const vy = velocity * Math.sin(θ);

        // 如果水平速度为0，无法到达目标
        if (Math.abs(vx) < 0.0001) {
            return NaN;
        }

        // 二次方程系数
        // a * t^2 + b * t + c = 0
        const a = -0.5 * g;
        const b = vy;
        const c = -vertical;

        // 判别式
        const delta = b * b - 4 * a * c;

        // 如果判别式小于0，无解
        if (delta < 0) {
            return NaN;
        }

        // 两个解
        const t1 = (-b + Math.sqrt(delta)) / (2 * a);
        const t2 = (-b - Math.sqrt(delta)) / (2 * a);

        // 水平运动时间
        const tx = horizontal / vx;

        // 选择合适的解
        let t: number;
        if (t1 > 0 && t2 > 0) {
            // 两个解都大于0，选择较小的那个
            t = Math.min(t1, t2);
        } else if (t1 > 0) {
            // 只有t1大于0
            t = t1;
        } else if (t2 > 0) {
            // 只有t2大于0
            t = t2;
        } else {
            // 两个解都小于0，无法到达目标
            return NaN;
        }

        // 如果垂直运动时间与水平运动时间相差太大，则认为无法到达目标
        // 对于高位置目标，放宽这个限制
        const timeDifference = Math.abs(t - tx);
        const timeThreshold = Math.max(0.5, tx * 0.2); // 允许 20% 的误差，最小 0.5 秒

        if (timeDifference > timeThreshold) {
            console.log(`垂直和水平运动时间相差过大: 垂直=${t.toFixed(2)}秒, 水平=${tx.toFixed(2)}秒, 差异=${timeDifference.toFixed(2)}秒, 阈值=${timeThreshold.toFixed(2)}秒`);
            return NaN;
        }

        return t;
    }

    /**
     * 计算指定时刻的位移距离
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param time 时间点
     * @param debug 是否输出调试信息
     * @returns 水平和垂直位移
     */
    public static calculateDisplacementAtMoment(angle: number, velocity: number, time: number, debug: boolean = false) {
        // 重力加速度（垂直向下）
        const g = ProjectileCalculator.gravity;
        // 初始角度（弧度制）
        const θ = angle * deg2Rad;

        // 水平位移
        // x = v * cos(θ) * t
        const x = velocity * Math.cos(θ) * time;

        // 垂直位移
        // y = v * sin(θ) * t - 0.5 * g * t^2
        const y = velocity * Math.sin(θ) * time - 0.5 * g * Math.pow(time, 2);

        if (debug) {
            console.log("位移计算", {
                angle,
                angleRad: θ,
                velocity,
                time,
                gravity: g,
                horizontalVelocity: velocity * Math.cos(θ),
                verticalVelocity: velocity * Math.sin(θ),
                horizontalDisplacement: x,
                verticalDisplacement: y
            });
        }

        return { x, y };
    }

    /**
     * 计算指定时刻的速度
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param time 时间点
     * @returns 水平和垂直速度
     */
    public static calculateVelocityAtMoment(angle: number, velocity: number, time: number) {
        // 重力加速度（垂直向下）
        const g = ProjectileCalculator.gravity;
        // 初始角度（弧度制）
        const θ = angle * deg2Rad;

        // 水平速度分量（保持不变）
        const vx = velocity * Math.cos(θ);
        // 垂直速度分量（受重力影响）
        const vy = velocity * Math.sin(θ) - g * time;

        return { vx, vy };
    }

    /**
     * 计算指定时刻的运动角度
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param time 时间
     * @param returnInRadians 是否返回弧度制结果
     * @param debug 是否输出调试信息
     * @returns 角度（角度制或弧度制）
     */
    public static calculateAngleAtMoment(angle: number, velocity: number, time: number, returnInRadians: boolean = false, debug: boolean = false) {
        // 计算该时刻的速度
        const { vx, vy } = ProjectileCalculator.calculateVelocityAtMoment(angle, velocity, time);

        // 计算角度（弧度制）
        const θ = Math.atan2(vy, vx);

        if (debug) {
            console.log("角度计算", {
                initialAngle: angle,
                initialVelocity: velocity,
                time,
                horizontalVelocity: vx,
                verticalVelocity: vy,
                angle: θ * rad2Deg
            });
        }

        // 根据参数返回弧度或角度
        return (returnInRadians ? θ : θ * rad2Deg);
    }

    /**
     * 根据初始角度计算初始速度
     * @param x 水平距离
     * @param y 垂直距离
     * @param angle 初始角度（角度制）
     * @returns 初始速度，如果无法到达目标则返回 NaN
     */
    public static calculateWithAngle(x: number, y: number, angle: number) {
        // 重力加速度（垂直向下）
        const g = ProjectileCalculator.gravity;
        // 初始角度（弧度制）
        const θ = angle * deg2Rad;

        // 速度公式
        // v = sqrt( ( x^2 * g ) / ( 2 * x * sin(θ) * cos(θ) - 2 * y * cos(θ)^2 ) )

        // 部分计算结果
        const p1 = (2 * x * Math.sin(θ) * Math.cos(θ)) - (2 * y * Math.pow(Math.cos(θ), 2));
        // 负数没有平方根
        if (p1 <= 0) {
            return NaN;
        }
        // 速度
        const v = Math.sqrt((g * Math.pow(x, 2)) / p1);

        return v;
    }

    /**
     * 根据初始速度计算初始角度
     * @param x 水平距离
     * @param y 垂直距离
     * @param velocity 初始速度
     * @returns 两个可能的角度（角度制），如果无法到达目标则返回 NaN
     */
    public static calculateWithVelocity(x: number, y: number, velocity: number) {
        // 重力加速度（垂直向下）
        const g = ProjectileCalculator.gravity;
        // 初始速度
        const v = velocity;

        // 角度公式
        // θ = atan( ( -v^2 ± sqrt( v^4 - g * ( g * x^2 + 2 * y * v^2 ) ) / ( -g * x ) ) )

        // 部分计算结果
        const p1 = Math.pow(v, 2);
        const p2 = Math.pow(v, 4) - g * (g * Math.pow(x, 2) + 2 * y * p1);
        // 负数没有平方根
        if (p2 < 0) {
            return {
                angle1: NaN,
                angle2: NaN,
            };
        }
        // 部分计算结果
        const p3 = Math.sqrt(p2);
        // 角度（两个解）
        const θ1 = Math.atan((-p1 + p3) / (-g * x));
        const θ2 = Math.atan((-p1 - p3) / (-g * x));

        return {
            angle1: θ1 * rad2Deg,
            angle2: θ2 * rad2Deg,
        };
    }

    /**
     * 计算抛射物的平均速度
     * @param distance 距离
     * @param time 时间
     * @returns 平均速度
     */
    public static calculateAverageVelocity(distance: number, time: number): number {
        if (time <= 0) {
            return 0;
        }
        return distance / time;
    }

    // ==================== 轨迹计算相关方法 ====================

    /**
     * 计算炮弹沿抛物线轨迹的实际移动距离
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param totalTime 总飞行时间
     * @returns 轨迹距离
     */
    public static calculateTrajectoryDistance(startPos: Vector3, endPos: Vector3, angle: number, velocity: number, totalTime: number): number {
        // 使用数值积分计算抛物线长度
        // 将飞行时间分成多个小段，计算每段的位移，然后累加
        const segments = 30; // 分段数
        const dt = totalTime / segments;
        let totalDistance = 0;
        let lastPos = Vector3Util.clone(startPos); // 上一个点的位置

        // 计算从起点到终点的方向向量
        const direction = Vector3Util.create();
        Vector3Util.subtract(direction, endPos, startPos);
        Vector3Util.normalize(direction, direction);

        // 投影到水平面并归一化
        const horizontalDir = VectorUtil.projectOnPlane(direction, Vector3Util.UP);
        Vector3Util.normalize(horizontalDir, horizontalDir);

        for (let i = 1; i <= segments; i++) {
            const t = i * dt;

            // 计算当前时刻的位置
            const displacement = ProjectileCalculator.calculateDisplacementAtMoment(
                angle,
                velocity,
                t
            );

            // 计算当前点的位置
            const currentPos = Vector3Util.create(
                startPos.x + horizontalDir.x * displacement.x,
                startPos.y + horizontalDir.y * displacement.x + displacement.y, // 考虑 Y 分量的水平位移
                startPos.z + horizontalDir.z * displacement.x
            );

            // 计算两点之间的距离并累加
            totalDistance += Math.sqrt(
                Math.pow(currentPos.x - lastPos.x, 2) +
                Math.pow(currentPos.y - lastPos.y, 2) +
                Math.pow(currentPos.z - lastPos.z, 2)
            );

            // 更新上一个点的位置
            Vector3Util.copy(lastPos, currentPos);
        }

        // 输出直线距离和轨迹距离的比较
        const linearDistance = Math.sqrt(
            Math.pow(endPos.x - startPos.x, 2) +
            Math.pow(endPos.y - startPos.y, 2) +
            Math.pow(endPos.z - startPos.z, 2)
        );
        console.log(`预测轨迹距离: ${totalDistance.toFixed(2)}，直线距离: ${linearDistance.toFixed(2)}，比例: ${(totalDistance / linearDistance).toFixed(2)}`);

        return totalDistance;
    }

    /**
     * 计算炮弹的预计平均速度（米/秒）
     * @param firePos 发射位置
     * @param targetPos 目标位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param timeScale 时间缩放因子
     * @param useTrajectoryDistance 是否使用轨迹距离而不是直线距离
     * @returns 预计平均速度（米/秒），如果无法到达目标则返回 NaN
     */
    public static calculateExpectedAverageVelocity(
        firePos: Vector3,
        targetPos: Vector3,
        angle: number,
        velocity: number,
        timeScale: number = 1.0,
        useTrajectoryDistance: boolean = true
    ): number {
        // 计算到达目标所需的时间（游戏内时间）
        let gameTimeToTarget = ProjectileCalculator.calculateTimeToTargetWithPos(firePos, targetPos, angle, velocity, false);

        // 如果无法到达目标，提供一个估计值
        if (isNaN(gameTimeToTarget)) {
            console.log(`无法计算到达目标所需的时间，提供估计值`);

            // 使用直线距离和初始速度的一半来估计
            const linearDistance = Math.sqrt(
                Math.pow(targetPos.x - firePos.x, 2) +
                Math.pow(targetPos.y - firePos.y, 2) +
                Math.pow(targetPos.z - firePos.z, 2)
            );
            const estimatedSpeed = velocity * 0.5; // 使用初始速度的一半作为平均速度
            const estimatedTime = linearDistance / estimatedSpeed;

            console.log(`估计到达时间: ${estimatedTime.toFixed(2)} 秒 (基于直线距离 ${linearDistance.toFixed(2)} 米和估计速度 ${estimatedSpeed.toFixed(2)} 米/秒)`);

            // 使用估计的时间继续计算
            gameTimeToTarget = estimatedTime;
        }

        // 计算实际时间（考虑时间缩放）
        const realTimeToTarget = gameTimeToTarget / timeScale;

        let distance = 0;

        if (useTrajectoryDistance) {
            // 计算沿抛物线轨迹的实际移动距离
            console.log(`预测轨迹计算 - 角度: ${angle.toFixed(2)}°, 速度: ${velocity.toFixed(2)}, 游戏内时间: ${gameTimeToTarget.toFixed(2)}秒, 实际时间: ${realTimeToTarget.toFixed(2)}秒`);
            distance = ProjectileCalculator.calculateTrajectoryDistance(firePos, targetPos, angle, velocity, gameTimeToTarget);
        } else {
            // 计算从发射点到目标点的直线距离
            distance = Math.sqrt(
                Math.pow(targetPos.x - firePos.x, 2) +
                Math.pow(targetPos.y - firePos.y, 2) +
                Math.pow(targetPos.z - firePos.z, 2)
            );
            console.log(`预测直线距离: ${distance.toFixed(2)}`);
        }

        // 平均速度 = 距离 / 时间
        // 使用实际时间（已考虑时间缩放）
        return distance / realTimeToTarget;
    }

    /**
     * 计算抛射物到达目标所需的时间（使用位置）
     * @param firePos 发射位置
     * @param targetPos 目标位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param considerTimeScale 是否考虑时间缩放
     * @param timeScale 时间缩放因子
     * @returns 到达目标所需的时间
     */
    public static calculateTimeToTargetWithPos(
        firePos: Vector3,
        targetPos: Vector3,
        angle: number,
        velocity: number,
        considerTimeScale: boolean = false,
        timeScale: number = 1.0
    ): number {
        const { horizontal, vertical } = ProjectileCalculator.calculateDisplacement(firePos, targetPos);

        // 计算基础时间
        const gameTime = ProjectileCalculator.calculateTimeToTargetPrecise(horizontal, vertical, angle, velocity);

        // 如果无法到达目标，返回 NaN
        if (isNaN(gameTime)) {
            return NaN;
        }

        // 如果需要考虑时间缩放，返回实际时间；否则返回游戏内时间
        return considerTimeScale ? gameTime / timeScale : gameTime;
    }

    /**
     * 根据期望的移动速度计算时间缩放
     * @param firePos 发射位置
     * @param targetPos 目标位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param moveSpeed 期望的移动速度
     * @returns 时间缩放因子
     */
    public static getMoveTimeScaleByMoveSpeed(
        firePos: Vector3,
        targetPos: Vector3,
        angle: number,
        velocity: number,
        moveSpeed: number
    ): number {
        let orgexpectedAverageVelocity = ProjectileCalculator.calculateExpectedAverageVelocity(
            firePos, targetPos, angle, velocity, 1, true
        );
        if (!orgexpectedAverageVelocity) return 1;
        return moveSpeed / orgexpectedAverageVelocity;
    }
}