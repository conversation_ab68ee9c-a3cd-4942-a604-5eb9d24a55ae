import { IState } from '../../core/StateMachine';
import { CannonWeapon } from './CannonWeapon';
import { WeaponState } from './WeaponsBase';
import { WeaponAttackingState } from './WeaponStates';

/**
 * 大炮攻击状态 - 扩展基础武器攻击状态
 */
export class CannonAttackingState extends WeaponAttackingState {
    // 重写owner属性为CannonWeapon类型
    protected override owner: CannonWeapon;
    private isCharging: boolean = false;
    private chargeTime: number = 0;

    constructor(owner: CannonWeapon) {
        super(owner);
        this.owner = owner;
    }

    onEnter(prevState?: IState): void {
        console.log('大炮进入攻击状态');
        this.isCharging = true;
        this.chargeTime = 0;
    }

    onUpdate(dt: number): void {
        if (this.owner.hasTargets()) {
            if (this.isCharging) {
                this.chargeTime += dt;
                if (this.chargeTime >= this.owner.maxChargeTime) {
                    // 发射攻击
                    this.owner.attack();
                    this.isCharging = false;
                    this.chargeTime = 0;
                    this.owner.changeState(WeaponState.IDLE);
                }
            } 
        } else {
            // 没有目标，回到空闲状态
            this.owner.changeState(WeaponState.IDLE);
        }
    }

    onExit(nextState?: IState): void {
        this.isCharging = false;
        this.chargeTime = 0;
        this.owner.resetCooldown();
    }
}
