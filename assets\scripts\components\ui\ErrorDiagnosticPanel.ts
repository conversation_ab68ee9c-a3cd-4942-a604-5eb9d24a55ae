import { _decorator, Component, Node, Label, Button, ScrollView, Layout, Color, instantiate, Prefab } from 'cc';
import { ErrorManager, ErrorSeverity } from '../../managers/ErrorManager';
import { GameEvent, GameEventMap } from '../../events/GameEvents';
import { EventManager } from '../../managers/EventManager';
import { Inject } from '../../decorators/Inject';

const { ccclass, property } = _decorator;

/**
 * 错误诊断面板
 * 提供开发调试工具，显示错误历史、统计和详细信息
 */
@ccclass('ErrorDiagnosticPanel')
export class ErrorDiagnosticPanel extends Component {
    // 错误管理器
    private _errorManager: ErrorManager = ErrorManager.getInstance();
    
    // 事件管理器
    @Inject('eventManager')
    private _eventManager: EventManager<GameEventMap>;
    
    // 引用错误条目预制体
    @property(Prefab)
    private errorItemPrefab: Prefab = null;
    
    // 引用错误列表内容节点
    @property(Node)
    private errorListContent: Node = null;
    
    // 引用错误统计标签
    @property(Label)
    private errorStatsLabel: Label = null;
    
    // 引用错误详细信息标签
    @property(Label)
    private errorDetailsLabel: Label = null;
    
    // 引用清除按钮
    @property(Button)
    private clearButton: Button = null;
    
    // 引用刷新按钮
    @property(Button)
    private refreshButton: Button = null;
    
    // 引用关闭按钮
    @property(Button)
    private closeButton: Button = null;
    
    // 严重程度对应的颜色
    private readonly _severityColors = {
        [ErrorSeverity.INFO]: new Color(100, 149, 237, 255),    // 蓝色
        [ErrorSeverity.WARNING]: new Color(255, 165, 0, 255),   // 橙色
        [ErrorSeverity.ERROR]: new Color(255, 69, 0, 255),      // 红色
        [ErrorSeverity.FATAL]: new Color(139, 0, 0, 255)        // 深红色
    };
    
    // 生命周期：组件加载
    onLoad() {
        // 初始化UI
        this.initializeUI();
        
        // 订阅错误事件
        this._eventManager.on(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        this._eventManager.on(GameEvent.ERROR_RECOVERED, this.onErrorRecovered, this);
        
        // 刷新错误列表和统计
        this.refreshErrorList();
        this.refreshErrorStats();
    }
    
    // 生命周期：组件启用
    onEnable() {
        // 面板显示时刷新数据
        this.refreshErrorList();
        this.refreshErrorStats();
    }
    
    // 生命周期：组件销毁
    onDestroy() {
        // 取消订阅错误事件
        this._eventManager.off(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        this._eventManager.off(GameEvent.ERROR_RECOVERED, this.onErrorRecovered, this);
    }
    
    // 初始化UI
    private initializeUI() {
        // 注册按钮事件
        if (this.clearButton) {
            this.clearButton.node.on(Button.EventType.CLICK, this.onClearButtonClicked, this);
        }
        
        if (this.refreshButton) {
            this.refreshButton.node.on(Button.EventType.CLICK, this.onRefreshButtonClicked, this);
        }
        
        if (this.closeButton) {
            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClicked, this);
        }
    }
    
    // 错误发生事件处理
    private onErrorOccurred(data: { error: Error, context: string, isFatal: boolean, timestamp: number }) {
        // 刷新错误列表和统计
        this.refreshErrorList();
        this.refreshErrorStats();
    }
    
    // 错误恢复事件处理
    private onErrorRecovered(data: { errorId: string, context: string, timestamp: number }) {
        // 刷新错误列表和统计
        this.refreshErrorList();
        this.refreshErrorStats();
    }
    
    // 刷新错误列表
    private refreshErrorList() {
        // 清空现有内容
        if (this.errorListContent) {
            this.errorListContent.removeAllChildren();
            
            // 获取错误历史
            const errorHistory = this._errorManager.getErrorHistory().reverse(); // 最新的在前面
            
            // 创建错误条目
            for (const errorInfo of errorHistory) {
                this.createErrorItem(errorInfo);
            }
        }
    }
    
    // 创建错误条目
    private createErrorItem(errorInfo: {
        id: string,
        error: Error,
        timestamp: number,
        severity: ErrorSeverity,
        context: string,
        recovered: boolean
    }) {
        if (!this.errorItemPrefab) return;
        
        const errorItem = instantiate(this.errorItemPrefab);
        this.errorListContent.addChild(errorItem);
        
        // 设置错误条目内容
        const timeLabel = errorItem.getChildByName('TimeLabel')?.getComponent(Label);
        const typeLabel = errorItem.getChildByName('TypeLabel')?.getComponent(Label);
        const messageLabel = errorItem.getChildByName('MessageLabel')?.getComponent(Label);
        const recoveredFlag = errorItem.getChildByName('RecoveredFlag');
        
        if (timeLabel) {
            const date = new Date(errorInfo.timestamp);
            timeLabel.string = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
        }
        
        if (typeLabel) {
            typeLabel.string = errorInfo.error.name;
            // 设置颜色
            typeLabel.color = this._severityColors[errorInfo.severity] || Color.WHITE;
        }
        
        if (messageLabel) {
            messageLabel.string = errorInfo.error.message;
        }
        
        if (recoveredFlag) {
            recoveredFlag.active = errorInfo.recovered;
        }
        
        // 添加点击事件以显示详细信息
        errorItem.on(Node.EventType.TOUCH_END, () => {
            this.showErrorDetails(errorInfo);
        });
    }
    
    // 显示错误详细信息
    private showErrorDetails(errorInfo: {
        id: string,
        error: Error,
        timestamp: number,
        severity: ErrorSeverity,
        context: string,
        recovered: boolean
    }) {
        if (!this.errorDetailsLabel) return;
        
        // 格式化时间
        const date = new Date(errorInfo.timestamp);
        const timeString = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
        
        // 构建详细信息文本
        let details = `错误ID: ${errorInfo.id}\n`;
        details += `发生时间: ${timeString}\n`;
        details += `类型: ${errorInfo.error.name}\n`;
        details += `严重程度: ${ErrorSeverity[errorInfo.severity]}\n`;
        details += `上下文: ${errorInfo.context}\n`;
        details += `状态: ${errorInfo.recovered ? '已恢复' : '未解决'}\n`;
        details += `消息: ${errorInfo.error.message}\n\n`;
        
        // 添加堆栈信息
        if (errorInfo.error.stack) {
            details += `堆栈: \n${errorInfo.error.stack}`;
        }
        
        this.errorDetailsLabel.string = details;
    }
    
    // 刷新错误统计
    private refreshErrorStats() {
        if (!this.errorStatsLabel) return;
        
        const stats = this._errorManager.getErrorStats();
        const pendingErrors = this._errorManager.getPendingErrors().length;
        
        // 构建统计信息文本
        let statsText = `总错误数: ${stats.totalErrors}\n`;
        statsText += `未解决错误: ${pendingErrors}\n`;
        statsText += `错误率: ${stats.errorRate.toFixed(2)} 错误/分钟\n\n`;
        
        // 按类型统计
        statsText += `按类型统计:\n`;
        for (const [type, count] of Object.entries(stats.byType)) {
            statsText += `- ${type}: ${count}\n`;
        }
        
        // 按严重程度统计
        statsText += `\n按严重程度统计:\n`;
        for (const [severity, count] of Object.entries(stats.bySeverity)) {
            statsText += `- ${severity}: ${count}\n`;
        }
        
        this.errorStatsLabel.string = statsText;
    }
    
    // 清除按钮点击事件
    private onClearButtonClicked() {
        this._errorManager.clearAllErrors();
        this.refreshErrorList();
        this.refreshErrorStats();
        
        if (this.errorDetailsLabel) {
            this.errorDetailsLabel.string = '';
        }
    }
    
    // 刷新按钮点击事件
    private onRefreshButtonClicked() {
        this.refreshErrorList();
        this.refreshErrorStats();
    }
    
    // 关闭按钮点击事件
    private onCloseButtonClicked() {
        this.node.active = false;
    }
    
    // 公开方法：显示面板
    public show() {
        this.node.active = true;
        this.refreshErrorList();
        this.refreshErrorStats();
    }
    
    // 公开方法：隐藏面板
    public hide() {
        this.node.active = false;
    }
}
