import { _decorator, Component, Node, input, Input, EventMouse, Vec3, sys, EventTouch, Vec2, EventKeyboard, KeyCode, Quat } from 'cc';
import { MathUtil } from '../utils/MathUtil';

const { ccclass, property } = _decorator;

/**
 * 摄像机控制器
 */
@ccclass('CameraController')
export class CameraController extends Component {

    @property({ type: Node, displayName: '摄像机节点' })
    protected cameraNode: Node = null;

    @property({ type: Node, displayName: '目标节点' })
    protected targetNode: Node = null;

    @property({ displayName: '最小距离' })
    protected minDistance: number = 5;

    @property({ displayName: '最大距离' })
    protected maxDistance: number = 50;

    @property({ displayName: '滚轮最小距离' })
    protected wheelMinDistance: number = 1;

    @property({ displayName: '滚轮最大距离' })
    protected wheelMaxDistance: number = 100;

    @property({ displayName: '平滑时间' })
    protected smoothTime: number = 0.1;

    @property({ displayName: '鼠标滚轮敏感度' })
    protected mouseSensitivity: number = 0.05;

    @property({ displayName: '双指触摸敏感度' })
    protected touchSensitivity: number = 0.05;

    @property({ displayName: '旋转敏感度' })
    protected rotationSensitivity: number = 0.3;

    @property({ displayName: '平移敏感度' })
    protected panSensitivity: number = 0.01;

    @property({ displayName: '键盘移动速度' })
    protected keyboardMoveSpeed: number = 0.1;

    @property({ displayName: '最小俯仰角（度）' })
    protected minPitch: number = -80;

    @property({ displayName: '最大俯仰角（度）' })
    protected maxPitch: number = 80;

    /**
     * 当前距离
     */
    protected currentDistance: number = 0;

    /**
     * 目标距离
     */
    protected targetDistance: number = 0;

    /**
     * 当前距离速度
     */
    protected currentDistanceVelocity: number = 0;

    /**
     * 当前旋转角度 - 俯仰角（上下）
     */
    protected currentPitch: number = 0;

    /**
     * 目标旋转角度 - 俯仰角（上下）
     */
    protected targetPitch: number = 0;

    /**
     * 当前俯仰角速度
     */
    protected currentPitchVelocity: number = 0;

    /**
     * 当前旋转角度 - 偏航角（左右）
     */
    protected currentYaw: number = 0;

    /**
     * 目标旋转角度 - 偏航角（左右）
     */
    protected targetYaw: number = 0;

    /**
     * 当前偏航角速度
     */
    protected currentYawVelocity: number = 0;

    /**
     * 当前位置偏移
     */
    protected currentOffset: Vec3 = new Vec3();

    /**
     * 目标位置偏移
     */
    protected targetOffset: Vec3 = new Vec3();

    /**
     * 当前位置偏移速度
     */
    protected currentOffsetVelocity: Vec3 = new Vec3();

    /**
     * 上一次双指滑动距离
     */
    protected lastTouchesDistance: number = null;

    /**
     * 上一次鼠标位置
     */
    protected lastMousePosition: Vec2 = null;

    /**
     * 是否正在拖动
     */
    protected isDragging: boolean = false;

    /**
     * 是否按下了键盘按键
     */
    protected keyStates: Map<KeyCode, boolean> = new Map();

    /**
     * 临时变量
     */
    protected tempVec3: Vec3 = new Vec3();
    protected tempQuat: Quat = new Quat();

    /**
     * 生命周期
     */
    protected onLoad() {
        this.init();
        this.registerEvent();
    }

    /**
     * 生命周期
     */
    protected onDestroy() {
        this.unregisterEvent();
    }

    /**
     * 注册事件
     */
    protected registerEvent() {
        if (sys.platform === sys.Platform.MOBILE_BROWSER) {
            // 手机
            input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
            input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
            input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
            input.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        } else {
            // 电脑
            input.on(Input.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
            input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.on(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
            input.on(Input.EventType.MOUSE_UP, this.onMouseUp, this);

            // 键盘事件
            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
            input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
        }
    }

    /**
     * 反注册事件
     */
    protected unregisterEvent() {
        if (sys.platform === sys.Platform.MOBILE_BROWSER) {
            // 手机
            input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
            input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
            input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
            input.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        } else {
            // 电脑
            input.off(Input.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
            input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
            input.off(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
            input.off(Input.EventType.MOUSE_UP, this.onMouseUp, this);

            // 键盘事件
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
            input.off(Input.EventType.KEY_UP, this.onKeyUp, this);
        }
    }

    /**
     * 初始化
     */
    protected init() {
        // 设置初始距离
        let distance: number;
        if (sys.platform === sys.Platform.MOBILE_BROWSER) {
            distance = 15;
        } else {
            distance = 8;
        }

        // 初始化摄像机位置和角度
        this.targetDistance = distance;
        this.currentDistance = distance;

        // 计算初始角度
        this.calculateInitialAngles();

        // 更新摄像机位置和旋转
        this.updateCameraTransform();
    }

    /**
     * 计算初始角度
     */
    protected calculateInitialAngles() {
        // 获取摄像机和目标节点的位置
        const cameraPos = this.cameraNode.getWorldPosition();
        const targetPos = this.targetNode.getWorldPosition();

        // 计算方向向量
        const direction = new Vec3();
        Vec3.subtract(direction, cameraPos, targetPos);
        direction.normalize();

        // 计算俯仰角（上下）- 与 Y 轴的夹角
        const pitchRadians = Math.asin(direction.y);
        this.currentPitch = this.targetPitch = pitchRadians * 180 / Math.PI;

        // 计算偏航角（左右）- 与 Z 轴的夹角
        const yawRadians = Math.atan2(direction.x, direction.z);
        this.currentYaw = this.targetYaw = yawRadians * 180 / Math.PI;

        console.log(`初始角度: 俯仰角=${this.currentPitch.toFixed(2)}°, 偏航角=${this.currentYaw.toFixed(2)}°`);
    }

    /**
     * 鼠标滚轮滚动回调
     * @param event
     */
    protected onMouseWheel(event: EventMouse) {
        const scroll = event.getScrollY() * this.mouseSensitivity;
        // 使用滚轮限制
        this.setTargetDistance(this.targetDistance - scroll, true);
    }

    /**
     * 鼠标按下回调
     * @param event
     */
    protected onMouseDown(event: EventMouse) {
        // 记录鼠标按下位置
        this.lastMousePosition = event.getLocation();
        this.isDragging = true;
    }

    /**
     * 鼠标移动回调
     * @param event
     */
    protected onMouseMove(event: EventMouse) {
        if (!this.isDragging || !this.lastMousePosition) {
            return;
        }

        const currentPos = event.getLocation();
        const deltaX = currentPos.x - this.lastMousePosition.x;
        const deltaY = currentPos.y - this.lastMousePosition.y;

        // 左键拖动 - 旋转
        if (event.getButton() === EventMouse.BUTTON_LEFT) {
            // 更新偏航角（左右旋转）- 反向控制
            this.targetYaw -= deltaX * this.rotationSensitivity; // 反向：鼠标向左移动时，相机向右旋转

            // 更新俯仰角（上下旋转）
            this.targetPitch -= deltaY * this.rotationSensitivity;

            // 限制俯仰角范围
            this.targetPitch = MathUtil.clamp(this.targetPitch, this.minPitch, this.maxPitch);
        }
        // 右键拖动 - 平移
        else if (event.getButton() === EventMouse.BUTTON_RIGHT) {
            // 计算相机的右方向和上方向
            const forward = new Vec3();
            const right = new Vec3();
            const up = new Vec3(0, 1, 0);

            // 根据当前偏航角计算前方和右方向
            const yawRadians = this.currentYaw * Math.PI / 180;
            forward.x = Math.sin(yawRadians);
            forward.y = 0;
            forward.z = Math.cos(yawRadians);

            // 计算右方向
            Vec3.cross(right, up, forward);
            right.normalize();

            // 计算平移量
            const rightOffset = right.multiplyScalar(deltaX * this.panSensitivity);
            const upOffset = up.multiplyScalar(deltaY * this.panSensitivity);

            // 更新目标偏移
            this.targetOffset.add(rightOffset);
            this.targetOffset.add(upOffset);
        }

        // 更新鼠标位置
        this.lastMousePosition = currentPos;
    }

    /**
     * 鼠标抬起回调
     * @param event
     */
    protected onMouseUp(_event: EventMouse) {
        this.isDragging = false;
    }

    /**
     * 键盘按下回调
     * @param event
     */
    protected onKeyDown(event: EventKeyboard) {
        this.keyStates.set(event.keyCode, true);
    }

    /**
     * 键盘抬起回调
     * @param event
     */
    protected onKeyUp(event: EventKeyboard) {
        this.keyStates.set(event.keyCode, false);
    }

    /**
     * 触摸开始回调
     * @param event
     */
    protected onTouchStart(event: EventTouch) {
        const touches = event.getAllTouches();

        // 单指触摸 - 旋转
        if (touches.length === 1) {
            this.lastMousePosition = touches[0].getLocation();
            this.isDragging = true;
        }
        // 双指触摸 - 缩放
        else if (touches.length >= 2) {
            this.lastTouchesDistance = Vec2.distance(touches[0].getLocation(), touches[1].getLocation());
        }
    }

    /**
     * 触摸移动回调
     * @param event
     */
    protected onTouchMove(event: EventTouch) {
        const touches = event.getAllTouches();

        // 单指触摸 - 旋转
        if (touches.length === 1 && this.isDragging && this.lastMousePosition) {
            const currentPos = touches[0].getLocation();
            const deltaX = currentPos.x - this.lastMousePosition.x;
            const deltaY = currentPos.y - this.lastMousePosition.y;

            // 更新偏航角（左右旋转）- 反向控制
            this.targetYaw -= deltaX * this.rotationSensitivity; // 反向：触摸向左移动时，相机向右旋转

            // 更新俯仰角（上下旋转）
            this.targetPitch -= deltaY * this.rotationSensitivity;

            // 限制俯仰角范围
            this.targetPitch = MathUtil.clamp(this.targetPitch, this.minPitch, this.maxPitch);

            // 更新触摸位置
            this.lastMousePosition = currentPos;
        }
        // 双指触摸 - 缩放
        else if (touches.length >= 2) {
            // 使用两个触摸点的距离差值来进行缩放
            const touchesDistance = Vec2.distance(touches[0].getLocation(), touches[1].getLocation());
            if (this.lastTouchesDistance !== null) {
                const diff = (touchesDistance - this.lastTouchesDistance) * this.touchSensitivity;
                // 使用滚轮限制
                this.setTargetDistance(this.targetDistance - diff, true);
            }
            this.lastTouchesDistance = touchesDistance;

            // 如果之前在拖动，现在停止
            if (this.isDragging) {
                this.isDragging = false;
                this.lastMousePosition = null;
            }
        }
    }

    /**
     * 触摸结束回调
     * @param event
     */
    protected onTouchEnd(_event: EventTouch) {
        this.lastTouchesDistance = null;
        this.isDragging = false;
        this.lastMousePosition = null;
    }

    /**
     * 生命周期
     * @param dt
     */
    protected update(_dt: number) {
        let needsUpdate = false;

        // 处理键盘输入
        this.processKeyboardInput();

        // 平滑插值距离
        if (this.currentDistance !== this.targetDistance) {
            const distanceResult = MathUtil.smoothDamp(
                this.currentDistance,
                this.targetDistance,
                this.currentDistanceVelocity,
                this.smoothTime
            );
            this.currentDistance = distanceResult.value;
            this.currentDistanceVelocity = distanceResult.velocity;
            needsUpdate = true;
        }

        // 平滑插值俯仰角
        if (this.currentPitch !== this.targetPitch) {
            const pitchResult = MathUtil.smoothDamp(
                this.currentPitch,
                this.targetPitch,
                this.currentPitchVelocity,
                this.smoothTime
            );
            this.currentPitch = pitchResult.value;
            this.currentPitchVelocity = pitchResult.velocity;
            needsUpdate = true;
        }

        // 平滑插值偏航角
        if (this.currentYaw !== this.targetYaw) {
            const yawResult = MathUtil.smoothDamp(
                this.currentYaw,
                this.targetYaw,
                this.currentYawVelocity,
                this.smoothTime
            );
            this.currentYaw = yawResult.value;
            this.currentYawVelocity = yawResult.velocity;
            needsUpdate = true;
        }

        // 平滑插值位置偏移
        if (!Vec3.equals(this.currentOffset, this.targetOffset)) {
            const offsetX = MathUtil.smoothDamp(
                this.currentOffset.x,
                this.targetOffset.x,
                this.currentOffsetVelocity.x,
                this.smoothTime
            );
            const offsetY = MathUtil.smoothDamp(
                this.currentOffset.y,
                this.targetOffset.y,
                this.currentOffsetVelocity.y,
                this.smoothTime
            );
            const offsetZ = MathUtil.smoothDamp(
                this.currentOffset.z,
                this.targetOffset.z,
                this.currentOffsetVelocity.z,
                this.smoothTime
            );

            this.currentOffset.x = offsetX.value;
            this.currentOffset.y = offsetY.value;
            this.currentOffset.z = offsetZ.value;

            this.currentOffsetVelocity.x = offsetX.velocity;
            this.currentOffsetVelocity.y = offsetY.velocity;
            this.currentOffsetVelocity.z = offsetZ.velocity;

            needsUpdate = true;
        }

        // 如果有任何变化，更新相机变换
        if (needsUpdate) {
            this.updateCameraTransform();
        }
    }

    /**
     * 处理键盘输入
     */
    protected processKeyboardInput() {
        // 如果没有键盘输入，直接返回
        if (this.keyStates.size === 0) {
            return;
        }

        // 计算相机的前方向、右方向和上方向
        const forward = new Vec3();
        const right = new Vec3();
        const worldUp = new Vec3(0, 1, 0);

        // 根据当前偏航角和俯仰角计算前方向（考虑相机的视线方向）
        const pitchRadians = this.currentPitch * Math.PI / 180;
        const yawRadians = this.currentYaw * Math.PI / 180;

        // 计算前方向（相机视线方向）
        forward.x = Math.sin(yawRadians) * Math.cos(pitchRadians);
        forward.y = Math.sin(pitchRadians);
        forward.z = Math.cos(yawRadians) * Math.cos(pitchRadians);
        forward.normalize();

        // 计算右方向（垂直于视线方向和世界上方向）
        Vec3.cross(right, worldUp, forward);
        right.normalize();

        // 计算相机的上方向（垂直于视线方向和右方向）
        const up = new Vec3();
        Vec3.cross(up, forward, right);
        up.normalize();

        // 移动速度
        const moveSpeed = this.keyboardMoveSpeed;

        // W/S - 沿视线方向前后移动（反向控制）
        if (this.keyStates.get(KeyCode.KEY_W)) {
            const offset = new Vec3(forward).multiplyScalar(-moveSpeed); // 反向：按W向后移动
            this.targetOffset.add(offset);
        }
        if (this.keyStates.get(KeyCode.KEY_S)) {
            const offset = new Vec3(forward).multiplyScalar(moveSpeed); // 反向：按S向前移动
            this.targetOffset.add(offset);
        }

        // A/D - 左右移动（垂直于视线方向）
        if (this.keyStates.get(KeyCode.KEY_A)) {
            const offset = new Vec3(right).multiplyScalar(-moveSpeed);
            this.targetOffset.add(offset);
        }
        if (this.keyStates.get(KeyCode.KEY_D)) {
            const offset = new Vec3(right).multiplyScalar(moveSpeed);
            this.targetOffset.add(offset);
        }

        // Q/E - 上下移动（相对于相机的上方向）
        if (this.keyStates.get(KeyCode.KEY_Q)) {
            const offset = new Vec3(up).multiplyScalar(-moveSpeed);
            this.targetOffset.add(offset);
        }
        if (this.keyStates.get(KeyCode.KEY_E)) {
            const offset = new Vec3(up).multiplyScalar(moveSpeed);
            this.targetOffset.add(offset);
        }

        // R/F - 拉近/推远
        if (this.keyStates.get(KeyCode.KEY_R)) {
            this.setTargetDistance(this.targetDistance - moveSpeed * 10);
        }
        if (this.keyStates.get(KeyCode.KEY_F)) {
            this.setTargetDistance(this.targetDistance + moveSpeed * 10);
        }
    }

    /**
     * 更新相机变换（位置和旋转）
     */
    protected updateCameraTransform() {
        // 计算相机位置
        // 1. 从原点开始
        const cameraPos = new Vec3(0, 0, 0);

        // 2. 根据俯仰角和偏航角计算方向
        const pitchRadians = this.currentPitch * Math.PI / 180;
        const yawRadians = this.currentYaw * Math.PI / 180;

        cameraPos.x = Math.sin(yawRadians) * Math.cos(pitchRadians);
        cameraPos.y = Math.sin(pitchRadians);
        cameraPos.z = Math.cos(yawRadians) * Math.cos(pitchRadians);

        // 3. 根据距离缩放
        cameraPos.multiplyScalar(this.currentDistance);

        // 4. 加上目标位置和偏移
        const targetPos = this.targetNode.getWorldPosition();
        cameraPos.add(targetPos);
        cameraPos.add(this.currentOffset);

        // 设置相机位置
        this.cameraNode.setWorldPosition(cameraPos);

        // 计算相机旋转 - 始终看向目标点
        const lookAtPos = new Vec3(targetPos);
        lookAtPos.add(this.currentOffset);
        this.cameraNode.lookAt(lookAtPos);
    }

    /**
     * 设置目标距离
     * @param value 距离值
     * @param useWheelLimits 是否使用滚轮限制（默认为false，使用键盘限制）
     */
    public setTargetDistance(value: number, useWheelLimits: boolean = false) {
        // 根据不同的控制方式应用不同的限制
        if (useWheelLimits) {
            // 使用滚轮限制
            this.targetDistance = MathUtil.clamp(value, this.wheelMinDistance, this.wheelMaxDistance);
        } else {
            // 使用键盘限制
            this.targetDistance = MathUtil.clamp(value, this.minDistance, this.maxDistance);
        }
    }

    /**
     * 设置目标偏航角（左右旋转）
     * @param value 角度值
     */
    public setTargetYaw(value: number) {
        this.targetYaw = value;
    }

    /**
     * 设置目标俯仰角（上下旋转）
     * @param value 角度值
     */
    public setTargetPitch(value: number) {
        this.targetPitch = MathUtil.clamp(value, this.minPitch, this.maxPitch);
    }

    /**
     * 设置目标位置偏移
     * @param offset 偏移向量
     */
    public setTargetOffset(offset: Vec3) {
        this.targetOffset.set(offset);
    }

    /**
     * 重置相机
     */
    public resetCamera() {
        // 重置距离（使用键盘限制）
        this.setTargetDistance(8, false);

        // 重置角度
        this.targetPitch = 0;
        this.targetYaw = 0;

        // 重置偏移
        this.targetOffset.set(Vec3.ZERO);
    }
}