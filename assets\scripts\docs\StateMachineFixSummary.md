# 状态机统一风格修复总结

## 问题描述

在统一状态机风格时遇到了运行时错误：

```
TypeError: Cannot set property name of #<EnemyIdleState> which has only a getter
    at StateMachine.registerState (StateMachine.ts:154:9)
```

## 问题原因

状态类中的 `name` 属性是通过 getter 定义的只读属性：

```typescript
export class EnemyIdleState implements IState {
    get name() { return 'idle'; }  // 只读属性，不能被重新赋值
    // ...
}
```

而 `registerState` 方法试图设置这个只读属性：

```typescript
// 错误的实现
public registerState(stateName: string, state: IState, isDefault: boolean = false): void {
    state.name = stateName;  // ❌ 试图设置只读属性
    this.addState(state, isDefault);
}
```

## 修复方案

### 1. 修复 `registerState` 方法 ✅

不再试图修改状态对象的 `name` 属性，而是直接使用传入的 `stateName` 作为键：

```typescript
public registerState(stateName: string, state: IState, isDefault: boolean = false): void {
    if (!state) {
        console.error('StateMachine: Cannot register null state');
        return;
    }

    // 直接使用传入的状态名称作为键，不修改状态对象的name属性
    // 这样可以避免与只读的name属性冲突
    this.states.set(stateName, state);

    // 如果是默认状态，设置为默认状态
    if (isDefault) {
        this.defaultState = state;
    }

    // 如果当前没有状态且这是第一个添加的状态，设置为当前状态
    if (!this.currentState && (isDefault || this.states.size === 1)) {
        this.changeState(stateName);
    }
}
```

### 2. 统一所有状态引用为枚举 ✅

#### 创建了统一的枚举定义

```typescript
// assets/scripts/enums/ProjectileState.ts
export enum ProjectileState {
    IDLE = 'idle',
    ACTIVE = 'active',
    COLLIDED = 'collided',
    EXPLODING = 'exploding',
    DESTROYED = 'destroyed'
}

export enum EnemyState {
    IDLE = 'idle',
    ATTACKING = 'attacking',
    HURT = 'hurt',
    DEAD = 'dead'
}
```

#### 更新了所有状态机注册

```typescript
// ProjectileCore
sm.registerState(ProjectileState.IDLE, new ProjectileIdleState(this), true);
sm.registerState(ProjectileState.ACTIVE, new ProjectileActiveState(this));

// EnemyBase
sm.registerState(EnemyState.IDLE, new EnemyIdleState(this), true);
sm.registerState(EnemyState.ATTACKING, new EnemyAttackingState(this));
```

#### 更新了所有状态切换

```typescript
// 之前：字符串
this.changeState('exploding');
this.changeState('dead');

// 现在：枚举
this.changeState(ProjectileState.EXPLODING);
this.changeState(EnemyState.DEAD);
```

#### 更新了所有状态比较

```typescript
// 之前：字符串比较
if (this.stateName === 'destroyed') { ... }
return targetState.name === 'idle';

// 现在：枚举比较
if (this.stateName === ProjectileState.DESTROYED) { ... }
return targetState.name === EnemyState.IDLE;
```

### 3. 更新了转换表格式 ✅

```typescript
// 统一使用枚举作为转换表的键值
sm.setTransitions(new Map([
    [ProjectileState.IDLE, [ProjectileState.ACTIVE, ProjectileState.DESTROYED]],
    [ProjectileState.ACTIVE, [ProjectileState.EXPLODING, ProjectileState.DESTROYED]],
    [ProjectileState.EXPLODING, [ProjectileState.DESTROYED]],
    [ProjectileState.DESTROYED, [ProjectileState.IDLE, ProjectileState.ACTIVE]]
]));
```

## 修复效果

### ✅ 解决了运行时错误
- 不再试图设置只读的 `name` 属性
- 状态机可以正常初始化和运行

### ✅ 实现了完全统一的风格
- 所有状态机都使用 `registerState` 方法
- 所有状态引用都使用枚举常量
- 所有转换表都使用统一格式

### ✅ 提高了类型安全性
- 编译时检查状态名称的正确性
- IDE 自动补全和重构支持
- 避免字符串拼写错误

### ✅ 保持了向后兼容性
- 原有的 `addState` 方法仍然可用
- 现有代码无需修改即可正常工作
- 支持渐进式迁移

## 最终架构

### 统一的状态机创建模式

```typescript
protected createStateMachine(): StateMachine {
    const sm = new StateMachine();
    
    // 使用 registerState 注册状态
    sm.registerState(StateEnum.STATE1, new State1(this), true);
    sm.registerState(StateEnum.STATE2, new State2(this));
    
    // 设置转换表
    sm.setTransitions(new Map([
        [StateEnum.STATE1, [StateEnum.STATE2]],
        [StateEnum.STATE2, [StateEnum.STATE1]]
    ]));
    
    return sm;
}
```

### 统一的状态切换模式

```typescript
// 使用枚举进行状态切换
this.changeState(StateEnum.TARGET_STATE);

// 使用枚举进行状态比较
if (this.stateName === StateEnum.CURRENT_STATE) {
    // 处理逻辑
}
```

## 总结

通过这次修复，我们成功地：

1. **解决了运行时错误** - 修复了只读属性冲突问题
2. **统一了状态机风格** - 所有状态机使用相同的 API 和模式
3. **提高了类型安全** - 使用枚举替代字符串，提供编译时检查
4. **保持了兼容性** - 现有代码无需修改即可正常工作
5. **改善了开发体验** - 一致的 API 和更好的 IDE 支持

现在整个项目的状态机都使用统一的、类型安全的、易于维护的风格，为未来的开发和维护奠定了坚实的基础！
