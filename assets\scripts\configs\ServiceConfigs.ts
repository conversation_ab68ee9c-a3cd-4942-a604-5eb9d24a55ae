import { ServiceConfig } from '../containers/DependencyContainer';
import { GameManager } from '../managers/GameManager';
import { ProjectileManager } from '../managers/ProjectileManager';
import { EventManager } from '../managers/EventManager';
import { EntityManager } from '../managers/EntityManager';
import { StateManager } from '../managers/StateManager';
import { EffectManager } from '../managers/EffectManager';
import { ErrorManager } from '../managers/ErrorManager';

/**
 * 游戏服务配置
 */
export const gameServiceConfigs: ServiceConfig[] = [
  {
    id: 'eventManager',
    type: EventManager,
    singleton: true
  },
  {
    id: 'projectileManager',
    type: ProjectileManager,
    singleton: true
  },
  {
    id: 'gameManager',
    type: GameManager,
    dependencies: ['projectileManager', 'eventManager'],
    singleton: true
  },

  {
    id: 'entityManager',
    type: EntityManager,
    singleton: true
  },
  {
    id: 'stateManager',
    type: StateManager,
    singleton: true
  },
  {
    id: 'effectManager',
    type: EffectManager,
    singleton: true
  },
  {
    id: 'errorManager',
    type: ErrorManager,
    dependencies: ['eventManager'],
    singleton: true
  }
];