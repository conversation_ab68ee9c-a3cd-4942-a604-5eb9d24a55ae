import { StatusEffect } from './weapons/WeaponEnums';

// 定义阵营枚举
export enum Faction {
    PLAYER = 'player',
    ENEMY = 'enemy',

}

// 定义角色类型枚举
export enum CharacterType {
    INFANTRY = 'INFANTRY',
    TANK = 'TANK',
    AIRCRAFT = 'AIRCRAFT',
    BOSS = 'BOSS'
}

// 定义角色状态枚举
export enum CharacterState {
    IDLE = 'idle',
    ATTACKING = 'attacking',
    HURT = 'hurt',
    DEAD = 'dead',

}

// 角色基类
export abstract class Character {
    protected _id: string;
    protected _faction: Faction;
    protected _type: CharacterType;
    protected _maxHealth: number;
    protected _currentHealth: number;
    protected _attackSpeed: number;
    protected _attackDamage: number;
    protected _defense: number;
    protected _moveSpeed: number;
    protected _isAlive: boolean;

    // 状态效果相关
    protected _activeStatusEffects: Set<StatusEffect> = new Set();

    // 添加位置属性
    protected _position: { x: number, y: number, z: number } = { x: 0, y: 0, z: 0 };

    constructor(
        faction: Faction,
        type: CharacterType,
        maxHealth: number,
        attackSpeed: number,
        attackDamage: number,
        defense: number,
        moveSpeed: number,
        position?: { x: number, y: number, z: number },

    ) {
        this._id = this.generateId();
        this._faction = faction;
        this._type = type;
        this._maxHealth = maxHealth;
        this._currentHealth = maxHealth;
        this._attackSpeed = attackSpeed;
        this._attackDamage = attackDamage;
        this._defense = defense;
        this._moveSpeed = moveSpeed;
        this._isAlive = true;
        this._position = position || { x: 0, y: 0, z: 0 };
        // 初始化状态机
    }

    // 生成唯一ID
    private generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // Getters
    get id(): string { return this._id; }
    get faction(): Faction { return this._faction; }
    get type(): CharacterType { return this._type; }
    get maxHealth(): number { return this._maxHealth; }
    get currentHealth(): number { return this._currentHealth; }
    get attackSpeed(): number { return this._attackSpeed; }
    get attackDamage(): number { return this._attackDamage; }
    get defense(): number { return this._defense; }
    get moveSpeed(): number { return this._moveSpeed; }
    get isAlive(): boolean { return this._isAlive; }
    get position(): { x: number, y: number, z: number } { return this._position; }

    // 基础方法
    takeDamage(damage: number): number {
        if (!this.isAlive) return 0;

        const actualDamage = Math.max(1, damage - this._defense);
        this._currentHealth -= actualDamage;

        if (this._currentHealth <= 0) {
            this._currentHealth = 0;
            this.onDeath();
        } else {
            // 受击时直接调用子类实现的 changeState 或 updateState
            // 例如：if (typeof this.changeState === 'function') this.changeState(CharacterState.HURT);
            // 或由子类负责状态切换
        }

        return actualDamage
    }

    heal(amount: number): void {
        if (!this._isAlive) return;
        this._currentHealth = Math.min(this._maxHealth, this._currentHealth + amount);
    }

    // 更新方法
    public update(deltaTime: number): void {
        this.onUpdate(deltaTime);

    }

    // 虚方法，供子类重写
    protected abstract onDeath(): void;
    protected abstract onUpdate(deltaTime: number): void;
    public abstract attack(): void;

    // 状态效果相关方法
    public addStatusEffect(effect: StatusEffect): void {
        if (effect !== StatusEffect.NONE) {
            this._activeStatusEffects.add(effect);
        }
    }

    public removeStatusEffect(effect: StatusEffect): void {
        this._activeStatusEffects.delete(effect);
    }

    public hasStatusEffect(effect: StatusEffect): boolean {
        return this._activeStatusEffects.has(effect);
    }

    public getActiveStatusEffects(): StatusEffect[] {
        return Array.from(this._activeStatusEffects);
    }
    protected moveUpdate(_dt: number): void { };
    public getVelocityVector(): { x: number, y: number, z: number } {
        return { x: 0, y: 0, z: 0 }
    }
    // 设置位置
    public setPosition(x: number, y: number, z: number): void {
        this._position = { x, y, z };
    }
}

