import { CannonWeapon } from '../entities/weapons/CannonWeapon';
import { ZombieEnemy } from '../entities/enemies/ZombieEnemy';

export class EntityManager {
    private weapons: CannonWeapon[] = [];
    private enemies: ZombieEnemy[] = [];

    addWeapon(weapon: CannonWeapon) {
        this.weapons.push(weapon);
    }

    addEnemy(enemy: ZombieEnemy) {
        this.enemies.push(enemy);
    }

    getWeapons() {
        return this.weapons;
    }

    getEnemies() {
        return this.enemies;
    }

    clear() {
        this.weapons = [];
        this.enemies = [];
    }
}
