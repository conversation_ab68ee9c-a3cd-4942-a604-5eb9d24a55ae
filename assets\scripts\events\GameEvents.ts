export enum GameEvent {
    // 游戏状态事件
    GAME_INITIALIZED = 'game:initialized',
    GAME_START = 'game:start',
    GAME_PAUSE = 'game:pause',
    GAME_RESUME = 'game:resume',
    GAME_OVER = 'game:over',
    BATTLE_START = 'battle_start',
    BATTLE_END = 'battle_end',
    ATTACK = 'attack',
    UNIT_DEATH = 'unit_death',
    
    // 错误处理事件
    ERROR_OCCURRED = 'error:occurred',
    ERROR_RECOVERED = 'error:recovered'
}

export interface GameEventMap {
    [GameEvent.GAME_START]: { timestamp: number };
    [GameEvent.GAME_OVER]: { reason: string };
    [GameEvent.GAME_PAUSE]: { damage: number; targetId: string };
    [GameEvent.GAME_RESUME]: { characterId: string; position: { x: number; y: number } };
    [GameEvent.BATTLE_START]: { timestamp: number };
    [GameEvent.BATTLE_END]: { isVictory: boolean; duration: number };
    [GameEvent.ATTACK]: { attacker: string; target: string; damage: number };
    [GameEvent.UNIT_DEATH]: { unitId: string; unitType: string };
    [GameEvent.ERROR_OCCURRED]: { error: Error; context: string; isFatal: boolean; timestamp: number; diagnosis?: any };
    [GameEvent.ERROR_RECOVERED]: { errorId: string; context: string; timestamp: number };
}