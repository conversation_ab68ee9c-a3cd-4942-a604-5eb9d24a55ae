/**
 * 快速位置调试工具
 * 专门用于排查炮弹击中位置和敌人位置偏移问题
 */
export class QuickPositionDebug {
    private static enabled: boolean = false;
    private static logCount: number = 0;
    private static maxLogs: number = 20;

    /**
     * 启用/禁用调试
     */
    public static setEnabled(enabled: boolean): void {
        QuickPositionDebug.enabled = enabled;
        QuickPositionDebug.logCount = 0;
        // console.log(`快速位置调试: ${enabled ? '已启用' : '已禁用'}`);
    }

    /**
     * 调试武器瞄准
     */
    public static debugWeaponAiming(
        weaponPos: { x: number, y: number, z: number },
        firePointPos: { x: number, y: number, z: number },
        targetPos: { x: number, y: number, z: number },
        weaponParams: { yaw: number, pitch: number, velocity: number }
    ): void {
        if (!QuickPositionDebug.enabled || QuickPositionDebug.logCount >= QuickPositionDebug.maxLogs) return;

        const distance = Math.sqrt(
            Math.pow(targetPos.x - firePointPos.x, 2) +
            Math.pow(targetPos.y - firePointPos.y, 2) +
            Math.pow(targetPos.z - firePointPos.z, 2)
        );

        console.group(`🎯 武器瞄准 #${QuickPositionDebug.logCount + 1}`);
        // console.log(`武器位置: (${weaponPos.x.toFixed(2)}, ${weaponPos.y.toFixed(2)}, ${weaponPos.z.toFixed(2)})`);
        // console.log(`火力点位置: (${firePointPos.x.toFixed(2)}, ${firePointPos.y.toFixed(2)}, ${firePointPos.z.toFixed(2)})`);
        // console.log(`目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)}, ${targetPos.z.toFixed(2)})`);
        // console.log(`距离: ${distance.toFixed(2)}, 偏航: ${weaponParams.yaw.toFixed(1)}°, 俯仰: ${weaponParams.pitch.toFixed(1)}°, 速度: ${weaponParams.velocity.toFixed(1)}`);
        console.groupEnd();
        
        QuickPositionDebug.logCount++;
    }

    /**
     * 调试敌人位置
     */
    public static debugEnemyPosition(
        enemyId: string,
        logicPos: { x: number, y: number, z: number },
        viewPos: { x: number, y: number, z: number },
        velocity: { x: number, y: number, z: number }
    ): void {
        if (!QuickPositionDebug.enabled || QuickPositionDebug.logCount >= QuickPositionDebug.maxLogs) return;

        const posDiff = Math.sqrt(
            Math.pow(viewPos.x - logicPos.x, 2) +
            Math.pow(viewPos.y - logicPos.y, 2) +
            Math.pow(viewPos.z - logicPos.z, 2)
        );

        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y + velocity.z * velocity.z);

        if (posDiff > 0.1 || speed > 0.01) {
            console.group(`👾 敌人位置 ${enemyId} #${QuickPositionDebug.logCount + 1}`);
            // console.log(`逻辑位置: (${logicPos.x.toFixed(2)}, ${logicPos.y.toFixed(2)}, ${logicPos.z.toFixed(2)})`);
            // console.log(`视图位置: (${viewPos.x.toFixed(2)}, ${viewPos.y.toFixed(2)}, ${viewPos.z.toFixed(2)})`);
            // console.log(`位置差异: ${posDiff.toFixed(3)}, 速度: ${speed.toFixed(3)}`);
            if (speed > 0.01) {
                // console.log(`速度向量: (${velocity.x.toFixed(3)}, ${velocity.y.toFixed(3)}, ${velocity.z.toFixed(3)})`);
            }
            console.groupEnd();
            QuickPositionDebug.logCount++;
        }
    }

    /**
     * 调试炮弹爆炸
     */
    public static debugBulletExplosion(
        targetPos: { x: number, y: number, z: number },
        explosionPos: { x: number, y: number, z: number },
        bulletStartPos: { x: number, y: number, z: number }
    ): void {
        if (!QuickPositionDebug.enabled || QuickPositionDebug.logCount >= QuickPositionDebug.maxLogs) return;

        const targetOffset = Math.sqrt(
            Math.pow(explosionPos.x - targetPos.x, 2) +
            Math.pow(explosionPos.y - targetPos.y, 2) +
            Math.pow(explosionPos.z - targetPos.z, 2)
        );

        const totalDistance = Math.sqrt(
            Math.pow(targetPos.x - bulletStartPos.x, 2) +
            Math.pow(targetPos.y - bulletStartPos.y, 2) +
            Math.pow(targetPos.z - bulletStartPos.z, 2)
        );

        console.group(`💥 炮弹爆炸 #${QuickPositionDebug.logCount + 1}`);
        // console.log(`发射位置: (${bulletStartPos.x.toFixed(2)}, ${bulletStartPos.y.toFixed(2)}, ${bulletStartPos.z.toFixed(2)})`);
        // console.log(`目标位置: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)}, ${targetPos.z.toFixed(2)})`);
        // console.log(`爆炸位置: (${explosionPos.x.toFixed(2)}, ${explosionPos.y.toFixed(2)}, ${explosionPos.z.toFixed(2)})`);
        // console.log(`目标偏差: ${targetOffset.toFixed(2)}, 总距离: ${totalDistance.toFixed(2)}, 偏差率: ${((targetOffset / totalDistance) * 100).toFixed(1)}%`);
        
        if (targetOffset > 2.0) {
            console.warn(`⚠️ 偏差过大! 可能的原因:`);
            console.warn(`   - 目标位置预测不准确`);
            console.warn(`   - 武器瞄准参数错误`);
            console.warn(`   - 坐标系统不一致`);
        }
        
        console.groupEnd();
        QuickPositionDebug.logCount++;
    }

    /**
     * 重置计数器
     */
    public static reset(): void {
        QuickPositionDebug.logCount = 0;
        console.log('快速位置调试计数器已重置');
    }

    /**
     * 获取状态
     */
    public static getStatus(): { enabled: boolean, logCount: number, maxLogs: number } {
        return {
            enabled: QuickPositionDebug.enabled,
            logCount: QuickPositionDebug.logCount,
            maxLogs: QuickPositionDebug.maxLogs
        };
    }
}

// 全局访问
(window as any).QuickPositionDebug = QuickPositionDebug;
