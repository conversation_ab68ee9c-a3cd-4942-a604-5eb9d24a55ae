import { _decorator, Component, Node, Vec3, Animation, AnimationState, Collider, RigidBody, CCFloat, CCInteger } from 'cc';
import { StateMachine, IState } from './StateMachine';
import { AttackState, DeathState, HitState, IdleState, MoveState, SpawnState } from './CharacterState';

const { ccclass, property } = _decorator;

/**
 * 角色基类
 * 所有角色（玩家、敌人等）都应该继承这个类
 */
@ccclass('Character')
export class Character extends Component {
    /**
     * 角色名称
     */
    @property({ displayName: '角色名称' })
    public characterName: string = 'Character';

    /**
     * 最大生命值
     */
    @property({ type: CCFloat, displayName: '最大生命值' })
    public maxHealth: number = 100;

    /**
     * 当前生命值
     */
    protected _currentHealth: number = 100;

    /**
     * 移动速度
     */
    @property({ type: CCFloat, displayName: '移动速度' })
    public moveSpeed: number = 5;

    /**
     * 旋转速度
     */
    @property({ type: CCFloat, displayName: '旋转速度' })
    public rotationSpeed: number = 180;

    /**
     * 攻击力
     */
    @property({ type: CCFloat, displayName: '攻击力' })
    public attackPower: number = 10;

    /**
     * 攻击范围
     */
    @property({ type: CCFloat, displayName: '攻击范围' })
    public attackRange: number = 2;

    /**
     * 攻击冷却时间（秒）
     */
    @property({ type: CCFloat, displayName: '攻击冷却时间（秒）' })
    public attackCooldown: number = 1;

    /**
     * 是否无敌
     */
    @property({ displayName: '是否无敌' })
    public isInvincible: boolean = false;

    /**
     * 是否已死亡
     */
    protected _isDead: boolean = false;

    /**
     * 上次攻击时间
     */
    protected lastAttackTime: number = 0;

    /**
     * 动画组件
     */
    protected animation: Animation = null;

    /**
     * 碰撞器
     */
    protected collider: Collider = null;

    /**
     * 刚体
     */
    protected rigidBody: RigidBody = null;

    /**
     * 状态机
     */
    protected stateMachine: StateMachine = null;

    /**
     * 当前动画状态
     */
    protected currentAnimState: AnimationState = null;

    /**
     * 移动方向
     */
    protected moveDirection: Vec3 = new Vec3(0, 0, 0);

    /**
     * 目标位置
     */
    protected targetPosition: Vec3 = null;

    /**
     * 获取当前生命值
     */
    public get currentHealth(): number {
        return this._currentHealth;
    }

    /**
     * 设置当前生命值
     */
    public set currentHealth(value: number) {
        // 限制生命值范围
        this._currentHealth = Math.max(0, Math.min(value, this.maxHealth));

        // 检查是否死亡
        if (this._currentHealth <= 0 && !this._isDead) {
            this._isDead = true;
            this.onDeath();
        }
    }

    /**
     * 获取是否已死亡
     */
    public get isDead(): boolean {
        return this._isDead;
    }

    /**
     * 生命周期：加载
     */
    protected onLoad(): void {
        // 获取组件
        this.animation = this.getComponent(Animation) || this.getComponentInChildren(Animation);
        this.collider = this.getComponent(Collider);
        this.rigidBody = this.getComponent(RigidBody);

        // 创建状态机
        this.stateMachine = this.addComponent(StateMachine);

        // 初始化状态
        this.initializeStates();

        // 初始化生命值
        this._currentHealth = this.maxHealth;
    }

    /**
     * 生命周期：启用
     */
    protected onEnable(): void {
        // 进入生成状态
        this.changeState('spawn');
    }

    /**
     * 生命周期：更新
     * @param dt 时间增量
     */
    protected update(dt: number): void {
        // 更新状态机
        if (this.stateMachine) {
            this.stateMachine.update(dt);
        }
    }

    /**
     * 初始化状态
     */
    protected initializeStates(): void {
        // 添加基本状态
        this.stateMachine.addState(new SpawnState(this));
        this.stateMachine.addState(new IdleState(this), true); // 设置空闲状态为默认状态
        this.stateMachine.addState(new MoveState(this));
        this.stateMachine.addState(new AttackState(this));
        this.stateMachine.addState(new HitState(this));
        this.stateMachine.addState(new DeathState(this));
    }

    /**
     * 切换状态
     * @param stateName 状态名称
     * @returns 是否成功切换
     */
    public changeState(stateName: string): boolean {
        if (this.stateMachine) {
            return this.stateMachine.changeState(stateName);
        }
        return false;
    }

    /**
     * 播放动画
     * @param animName 动画名称
     * @returns 是否成功播放
     */
    public playAnimation(animName: string): boolean {
        if (!this.animation) {
            console.warn(`Character: Animation component not found on ${this.node.name}`);
            return false;
        }

        // 检查动画是否存在
        if (!this.animation.clips.find(clip => clip.name === animName)) {
            console.warn(`Character: Animation '${animName}' not found on ${this.node.name}`);
            return false;
        }

        // 播放动画
        this.animation.play(animName);
        // 获取动画状态
        this.currentAnimState = this.animation.getState(animName);
        return true;
    }

    /**
     * 检查动画是否播放完毕
     * @returns 是否播放完毕
     */
    public isAnimationFinished(): boolean {
        if (!this.currentAnimState) {
            return true;
        }
        return this.currentAnimState.isPlaying === false ||
               this.currentAnimState.time >= this.currentAnimState.duration;
    }

    /**
     * 更新移动
     * @param dt 时间增量
     */
    public updateMovement(dt: number): void {
        if (!this.moveDirection.equals(Vec3.ZERO)) {
            // 计算移动距离
            const distance = this.moveSpeed * dt;

            // 计算新位置
            const newPosition = new Vec3();
            Vec3.multiplyScalar(newPosition, this.moveDirection, distance);
            Vec3.add(newPosition, this.node.position, newPosition);

            // 设置新位置
            this.node.setPosition(newPosition);

            // 更新朝向
            this.updateRotation(dt);
        }
    }

    /**
     * 更新朝向
     * @param dt 时间增量
     */
    protected updateRotation(dt: number): void {
        if (!this.moveDirection.equals(Vec3.ZERO)) {
            // 计算目标旋转角度
            const targetRotation = Math.atan2(this.moveDirection.x, this.moveDirection.z) * 180 / Math.PI;

            // 获取当前旋转角度
            const currentRotation = this.node.eulerAngles.y;

            // 计算旋转差值
            let rotationDiff = targetRotation - currentRotation;

            // 标准化差值到 -180 到 180 之间
            if (rotationDiff > 180) rotationDiff -= 360;
            if (rotationDiff < -180) rotationDiff += 360;

            // 计算这一帧的旋转量
            const rotationAmount = Math.min(Math.abs(rotationDiff), this.rotationSpeed * dt) * Math.sign(rotationDiff);

            // 设置新的旋转
            this.node.setRotationFromEuler(0, currentRotation + rotationAmount, 0);
        }
    }

    /**
     * 设置移动方向
     * @param direction 方向向量
     */
    public setMoveDirection(direction: Vec3): void {
        // 归一化方向向量
        if (!direction.equals(Vec3.ZERO)) {
            Vec3.normalize(this.moveDirection, direction);
        } else {
            this.moveDirection.set(Vec3.ZERO);
        }

        // 如果有移动方向且不在移动状态，切换到移动状态
        if (!this.moveDirection.equals(Vec3.ZERO) && !this.stateMachine.isInState('move')) {
            this.changeState('move');
        }
        // 如果没有移动方向且在移动状态，切换到空闲状态
        else if (this.moveDirection.equals(Vec3.ZERO) && this.stateMachine.isInState('move')) {
            this.changeState('idle');
        }
    }

    /**
     * 设置目标位置
     * @param position 目标位置
     */
    public setTargetPosition(position: Vec3): void {
        this.targetPosition = position;

        // 计算移动方向
        if (this.targetPosition) {
            const direction = new Vec3();
            Vec3.subtract(direction, this.targetPosition, this.node.position);
            direction.y = 0; // 忽略垂直方向
            this.setMoveDirection(direction);
        }
    }

    /**
     * 尝试攻击
     * @returns 是否成功攻击
     */
    public tryAttack(): boolean {
        // 检查是否在攻击冷却中
        const currentTime = Date.now() / 1000;
        if (currentTime - this.lastAttackTime < this.attackCooldown) {
            return false;
        }

        // 切换到攻击状态
        if (this.changeState('attack')) {
            this.lastAttackTime = currentTime;
            return true;
        }

        return false;
    }

    /**
     * 受到伤害
     * @param damage 伤害值
     * @param attacker 攻击者
     * @returns 实际造成的伤害
     */
    public takeDamage(damage: number, attacker?: Character): number {
        // 如果已死亡或处于无敌状态，不受伤害
        if (this._isDead || this.isInvincible) {
            return 0;
        }

        // 计算实际伤害（可以在子类中重写以实现护甲等效果）
        const actualDamage = this.calculateDamage(damage, attacker);

        // 减少生命值
        const oldHealth = this._currentHealth;
        this.currentHealth -= actualDamage;

        // 如果没有死亡且受到了伤害，进入受击状态
        if (!this._isDead && actualDamage > 0) {
            this.onHit(actualDamage, attacker);
        }

        return oldHealth - this._currentHealth;
    }

    /**
     * 计算实际伤害
     * @param damage 原始伤害
     * @param attacker 攻击者
     * @returns 实际伤害
     */
    protected calculateDamage(damage: number, attacker?: Character): number {
        // 基类简单返回原始伤害，子类可以重写以实现更复杂的伤害计算
        return damage;
    }

    /**
     * 受击回调
     * @param damage 伤害值
     * @param attacker 攻击者
     */
    protected onHit(damage: number, attacker?: Character): void {
        // 切换到受击状态
        this.changeState('hit');
    }

    /**
     * 死亡回调
     */
    protected onDeath(): void {
        // 切换到死亡状态
        this.changeState('death');

        // 禁用碰撞器和刚体
        if (this.collider) {
            this.collider.enabled = false;
        }
        if (this.rigidBody) {
            this.rigidBody.enabled = false;
        }
    }
}
