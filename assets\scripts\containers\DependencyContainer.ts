import { IGameManager, IProjectileManager } from '../interfaces/IManager';
 

/**
 * 服务接口 - 定义服务生命周期方法
 */
export interface IService {
  /**
   * 初始化服务
   * 在服务被注册后调用
   */
  initialize?(): Promise<void> | void;

  /**
   * 销毁服务
   * 在容器被销毁或服务被移除时调用
   */
  dispose?(): Promise<void> | void;
}

/**
 * 服务配置
 */
export interface ServiceConfig {
  /**
   * 服务ID
   */
  id: string;

  /**
   * 服务类型
   */
  type: new (...args: any[]) => any;

  /**
   * 依赖的服务ID列表
   */
  dependencies?: string[];

  /**
   * 是否为单例
   * 默认为 true
   */
  singleton?: boolean;
}

/**
 * 依赖容器 - 统一的服务管理容器
 * 负责服务的注册、解析和生命周期管理
 */
export class DependencyContainer {
  private static _instance: DependencyContainer;
  private _services: Map<string, any> = new Map();
  private _initialized: boolean = false;
  private _configs: Map<string, ServiceConfig> = new Map();

  /**
   * 私有构造函数，防止外部直接实例化
   */
  private constructor() { }

  /**
   * 获取依赖容器单例
   */
  public static getInstance(): DependencyContainer {
    if (!this._instance) {
      this._instance = new DependencyContainer();
    }
    return this._instance;
  }

  /**
   * 初始化容器
   * 根据配置批量注册并初始化所有服务
   */
  public async initialize(): Promise<void> {
    if (this._initialized) return;

    console.log('Initializing dependency container...');

    // 根据配置批量创建所有服务
    for (const serviceId of this._configs.keys()) {
      if (!this.has(serviceId)) {
        this.createFromConfig(serviceId);
      }
    }

    // 初始化所有服务
    await this.initializeServices();

    this._initialized = true;
    console.log('Dependency container initialized');
  }

  /**
   * 注册服务
   * @param serviceId 服务标识符
   * @param instance 服务实例
   */
  public register<T>(serviceId: string, instance: T): void {
    this._services.set(serviceId, instance);
    console.log(`Service registered: ${serviceId}`);
  }

  /**
   * 获取服务
   * @param serviceId 服务标识符
   * @returns 服务实例
   * @throws 如果服务未注册，则抛出错误
   */
  public resolve<T>(serviceId: string): T {
    if (!this._services.has(serviceId)) {
      throw new Error(`Service ${serviceId} not registered`);
    }
    return this._services.get(serviceId) as T;
  }

  /**
   * 安全地获取服务，如果服务不存在则返回 null
   * @param serviceId 服务标识符
   * @returns 服务实例或 null
   */
  public tryResolve<T>(serviceId: string): T | null {
    return this._services.has(serviceId) ? this._services.get(serviceId) as T : null;
  }

  /**
   * 检查服务是否已注册
   * @param serviceId 服务标识符
   * @returns 是否已注册
   */
  public has(serviceId: string): boolean {
    return this._services.has(serviceId);
  }

  /**
   * 移除服务
   * @param serviceId 服务标识符
   */
  public async remove(serviceId: string): Promise<void> {
    if (!this._services.has(serviceId)) return;

    const service = this._services.get(serviceId);
    if (service && typeof service.dispose === 'function') {
      await service.dispose();
    }

    this._services.delete(serviceId);
    console.log(`Service removed: ${serviceId}`);
  }

  /**
   * 清除所有注册的服务
   */
  public async clear(): Promise<void> {
    // 销毁所有服务
    for (const [serviceId, service] of this._services.entries()) {
      if (service && typeof service.dispose === 'function') {
        await service.dispose();
      }
    }

    this._services.clear();
    this._initialized = false;
    console.log('All services cleared');
  }

  /**
   * 从配置注册服务
   * @param configs 服务配置列表
   */
  public registerFromConfig(configs: ServiceConfig[]): void {
    for (const config of configs) {
      this._configs.set(config.id, config);
    }
  }

  /**
   * 根据配置创建服务
   * @param serviceId 服务标识符
   */
  public createFromConfig(serviceId: string): any {
    const config = this._configs.get(serviceId);
    if (!config) {
      throw new Error(`No configuration found for service: ${serviceId}`);
    }

    // 解析依赖
    const dependencies = config.dependencies || [];
    const resolvedDeps = dependencies.map(depId => this.resolve(depId));

    // 创建服务实例
    const instance = new config.type(...resolvedDeps);

    // 如果是单例，注册到容器
    if (config.singleton !== false) {
      this.register(serviceId, instance);
    }

    return instance;
  }

  /**
   * 获取游戏管理器
   * 便捷方法，等同于 resolve<IGameManager>('gameManager')
   */
  public getGameManager(): IGameManager {
    return this.resolve<IGameManager>('gameManager');
  }

  /**
   * 获取投射物管理器
   * 便捷方法，等同于 resolve<IProjectileManager>('projectileManager')
   */
  public getProjectileManager(): IProjectileManager {
    return this.resolve<IProjectileManager>('projectileManager');
  }

  /**
   * 初始化所有服务
   * 私有方法，在初始化时调用
   */
  private async initializeServices(): Promise<void> {
    console.log('Initializing services...');

    for (const [serviceId, service] of this._services.entries()) {
      if (service && typeof service.initialize === 'function') {
        try {
          await service.initialize();
          console.log(`Service initialized: ${serviceId}`);
        } catch (error) {
          console.error(`Error initializing service ${serviceId}:`, error);
        }
      }
    }
  }
}
