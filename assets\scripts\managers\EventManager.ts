// 事件管理器，支持类型安全、生命周期管理和一次性监听
export class EventManager<T extends Record<string, any>> {
    // 单例实例
    private static _instance: EventManager<any>;

    // 存储所有事件监听器，按事件名分类，每个监听器可选绑定 owner
    private listeners = new Map<keyof T, Set<{ fn: (data: any) => void, owner?: object }>>();

    /**
     * 获取单例实例
     */
    public static getInstance<T extends Record<string, any>>(): EventManager<T> {
        if (!this._instance) {
            this._instance = new EventManager<T>();
        }
        return this._instance as EventManager<T>;
    }

    /**
     * 注册事件监听器
     * @param event 事件名
     * @param listener 事件回调函数
     * @param owner 可选，监听器所属对象，用于生命周期管理
     */
    on<K extends keyof T>(event: K, listener: (data: T[K]) => void, owner?: object): void {
        if (!this.listeners.has(event)) this.listeners.set(event, new Set());
        this.listeners.get(event)!.add({ fn: listener as any, owner });
    }

    /**
     * 移除事件监听器
     * @param event 事件名
     * @param listener 事件回调函数，不传则移除该事件所有监听器
     * @param owner 可选，监听器所属对象，用于精确匹配
     */
    off<K extends keyof T>(event: K, listener?: (data: T[K]) => void, owner?: object): void {
        const set = this.listeners.get(event);
        if (!set) return;

        // 如果没有提供监听器，则清除所有监听器或特定所有者的监听器
        if (!listener) {
            if (owner) {
                // 只移除特定所有者的监听器
                for (const item of Array.from(set)) {
                    if (item.owner === owner) set.delete(item);
                }
            } else {
                // 移除所有监听器
                set.clear();
            }
            return;
        }

        // 移除特定监听器
        for (const item of Array.from(set)) {
            if (item.fn === listener && (!owner || item.owner === owner)) {
                set.delete(item);
            }
        }
    }

    /**
     * 触发事件，通知所有监听器
     * @param event 事件名
     * @param data 事件数据
     */
    emit<K extends keyof T>(event: K, data: T[K]): void {
        this.listeners.get(event)?.forEach(item => item.fn(data));
    }

    /**
     * 移除指定 owner 的所有监听器，常用于对象销毁时自动清理
     * @param owner 监听器所属对象
     */
    offAllForOwner(owner: object): void {
        for (const set of this.listeners.values()) {
            for (const item of Array.from(set)) {
                if (item.owner === owner) set.delete(item);
            }
        }
    }

    /**
     * 注册一次性事件监听器，事件触发后自动移除
     * @param event 事件名
     * @param listener 事件回调函数
     * @param owner 可选，监听器所属对象
     */
    once<K extends keyof T>(event: K, listener: (data: T[K]) => void, owner?: object): void {
        const wrapper = (data: T[K]) => {
            this.off(event, wrapper);
            listener(data);
        };
        this.on(event, wrapper, owner);
    }
}

// 新增工厂函数，便于依赖注入注册
export function createEventManager<T extends Record<string, any>>() {
    return new EventManager<T>();
}