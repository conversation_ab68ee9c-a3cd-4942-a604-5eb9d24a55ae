import { Prefab, Node, Vec3, instantiate } from 'cc';
import { EnemyBase } from './EnemyBase';

import { ZombieEnemyView } from '../../components/enemies/ZombieEnemyView';


export class ZombieEnemy extends EnemyBase {
    constructor(
        path: Vec3[],
        maxHealth: number,
        attackSpeed: number,
        attackDamage: number,
        defense: number,
        moveSpeed: number,
        reward: number,
        prefab: Prefab,
        parent: Node,
        gameManager: any
    ) {
        super(path, maxHealth, attackSpeed, attackDamage, defense, moveSpeed, reward, prefab, parent, gameManager);
    }

    // 修正 createView 访问级别为 public，兼容 EnemyBase 的 public 声明
    public override createView(prefab: Prefab, parent: Node): ZombieEnemyView {
        const node = instantiate(prefab);
        const view = node.getComponent(ZombieEnemyView);
        if (!view) {
            throw new Error('Failed to get ZombieEnemyView component from prefab.');
        }
        view.node.parent = parent;
        view.setLogic(this as unknown as EnemyBase);
        return view;
    }
}
