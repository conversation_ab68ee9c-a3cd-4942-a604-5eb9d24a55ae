import { IState } from '../../core/StateMachine';
import { ProjectileCore } from './ProjectileCore';
import { BulletCore } from './BulletCore';
import { Vec3 } from 'cc';
import { ProjectileState } from '../../enums/ProjectileState';

/**
 * 通用投射物状态类 - 空闲状态
 */
export class ProjectileIdleState implements IState {
    protected owner: ProjectileCore;

    constructor(owner: ProjectileCore) {
        this.owner = owner;
    }

    get name() { return 'idle'; }

    onEnter(_prevState?: IState): void {
        // 进入空闲状态时的逻辑
    }

    onUpdate(_dt: number): void {
        // 空闲状态下不执行特殊逻辑
    }

    onExit(_nextState?: IState): void {
        // 退出空闲状态的逻辑
    }

    canTransitionTo(_targetState: IState): boolean {
        // 空闲状态可以转换到活跃或销毁状态
        return _targetState.name === ProjectileState.ACTIVE || _targetState.name === ProjectileState.DESTROYED;
    }
}

/**
 * 通用投射物状态类 - 活跃状态
 */
export class ProjectileActiveState implements IState {
    protected owner: ProjectileCore;

    constructor(owner: ProjectileCore) {
        this.owner = owner;
    }

    get name() { return 'active'; }

    onEnter(_prevState?: IState): void {
        // 进入活跃状态时重置相关属性
    }

    onUpdate(dt: number): void {
        // 执行正常的更新逻辑，委托给具体的模式
        this.owner.updateProjectile(dt);
    }

    onExit(_nextState?: IState): void {
        // 退出活跃状态的逻辑
    }

    canTransitionTo(_targetState: IState): boolean {
        // 活跃状态可以转换到碰撞、爆炸或销毁状态
        return _targetState.name === ProjectileState.COLLIDED ||
               _targetState.name === ProjectileState.EXPLODING ||
               _targetState.name === ProjectileState.DESTROYED;
    }
}

/**
 * 通用投射物状态类 - 碰撞状态
 */
export class ProjectileCollidedState implements IState {
    protected owner: ProjectileCore;
    protected collisionPosition: Vec3 = null;
    protected collisionNormal: Vec3 = null;

    constructor(owner: ProjectileCore) {
        this.owner = owner;
    }

    get name() { return 'collided'; }

    /**
     * 设置碰撞信息
     */
    public setCollisionInfo(position: Vec3, normal?: Vec3): void {
        this.collisionPosition = position;
        this.collisionNormal = normal;
    }

    onEnter(_prevState?: IState): void {
        // 触发碰撞效果
        this.owner.onCollision(this.collisionPosition, this.collisionNormal);
    }

    onUpdate(_dt: number): void {
        // 碰撞状态通常不需要更新，可能有一些碰撞后效果的处理
    }

    onExit(_nextState?: IState): void {
        // 退出碰撞状态，清理资源
        this.collisionPosition = null;
        this.collisionNormal = null;
    }

    canTransitionTo(_targetState: IState): boolean {
        // 碰撞状态只能转换到销毁状态
        return _targetState.name === ProjectileState.DESTROYED;
    }
}

/**
 * 通用投射物状态类 - 销毁状态
 */
export class ProjectileDestroyedState implements IState {
    protected owner: ProjectileCore;

    constructor(owner: ProjectileCore) {
        this.owner = owner;
    }

    get name() { return 'destroyed'; }

    onEnter(_prevState?: IState): void {
        // 执行销毁逻辑
        console.log(`ProjectileDestroyedState: 进入销毁状态，执行销毁逻辑`);
        this.owner.performDestroy();
    }

    onUpdate(_dt: number): void {
        // 销毁状态不需要更新
    }

    onExit(_nextState?: IState): void {
        // 销毁状态不应该退出
    }

    canTransitionTo(_targetState: IState): boolean {
        // 销毁状态可以转换到空闲状态或活跃状态，以支持对象池重用
        return _targetState.name === ProjectileState.IDLE || _targetState.name === ProjectileState.ACTIVE;
    }
}

/**
 * 子弹特有状态 - 爆炸状态
 */
export class BulletExplodingState implements IState {
    protected owner: BulletCore;
    protected position: Vec3 = null;
    protected normal: Vec3 = null;

    constructor(owner: BulletCore) {
        this.owner = owner;
    }

    get name() { return 'exploding'; }

    /**
     * 设置爆炸信息
     */
    public setExplosionInfo(position: Vec3, normal?: Vec3): void {
        this.position = position;
        this.normal = normal;
    }

    onEnter(_prevState?: IState): void {
        // 执行爆炸效果
        console.log(`BulletExplodingState: 进入爆炸状态，执行爆炸效果`);
        this.owner.explode(this.position, this.normal);
    }

    onUpdate(_dt: number): void {
        // 爆炸状态下的更新逻辑
        // 可以添加爆炸持续时间计时等
    }

    onExit(_nextState?: IState): void {
        // 退出爆炸状态的逻辑
        this.position = null;
        this.normal = null;
    }

    canTransitionTo(_targetState: IState): boolean {
        // 爆炸状态可以转换到销毁状态
        return _targetState.name === ProjectileState.DESTROYED;
    }
}
