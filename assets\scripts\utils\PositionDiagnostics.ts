import { Vec3 } from 'cc';
import { EnemyBase } from '../entities/enemies/EnemyBase';
import { WeaponsBase } from '../entities/weapons/WeaponsBase';

/**
 * 位置诊断工具
 * 用于排查炮弹击中位置和敌人位置偏移问题
 */
export class PositionDiagnostics {
    private static instance: PositionDiagnostics;
    private isEnabled: boolean = true;
    private logCount: number = 0;
    private maxLogs: number = 50; // 限制日志数量，避免刷屏

    public static getInstance(): PositionDiagnostics {
        if (!PositionDiagnostics.instance) {
            PositionDiagnostics.instance = new PositionDiagnostics();
        }
        return PositionDiagnostics.instance;
    }

    /**
     * 启用或禁用诊断
     */
    public setEnabled(enabled: boolean): void {
        this.isEnabled = enabled;
        if (enabled) {
            this.logCount = 0;
            console.log('位置诊断已启用');
        } else {
            console.log('位置诊断已禁用');
        }
    }

    /**
     * 诊断敌人位置同步问题
     */
    public diagnoseEnemyPosition(enemy: EnemyBase): void {
        if (!this.isEnabled || this.logCount >= this.maxLogs) return;

        const logicPos = enemy.position;
        const view = enemy.getView();

        if (view && view.node && view.node.isValid) {
            const viewWorldPos = view.node.getWorldPosition();
            const viewLocalPos = view.node.getPosition();

            // 计算位置差异
            const worldPosDiff = this.calculateDistance(logicPos, {
                x: viewWorldPos.x,
                y: viewWorldPos.y,
                z: viewWorldPos.z
            });

            if (worldPosDiff > 0.1) {
                console.group(`🔍 敌人位置诊断 - ID: ${enemy.id}`);
                console.log(`逻辑位置: (${logicPos.x.toFixed(3)}, ${logicPos.y.toFixed(3)}, ${logicPos.z.toFixed(3)})`);
                console.log(`视图世界位置: (${viewWorldPos.x.toFixed(3)}, ${viewWorldPos.y.toFixed(3)}, ${viewWorldPos.z.toFixed(3)})`);
                console.log(`视图本地位置: (${viewLocalPos.x.toFixed(3)}, ${viewLocalPos.y.toFixed(3)}, ${viewLocalPos.z.toFixed(3)})`);
                console.log(`位置差异: ${worldPosDiff.toFixed(3)}`);
                console.log(`敌人状态: ${enemy.currentStateName}, 存活: ${enemy.isAlive}`);
                console.groupEnd();
                this.logCount++;
            }
        }
    }

    /**
     * 诊断武器瞄准问题
     */
    public diagnoseWeaponAiming(weapon: WeaponsBase, targetPos: { x: number, y: number, z: number }): void {
        if (!this.isEnabled || this.logCount >= this.maxLogs) return;

        const weaponLogicPos = weapon.position;
        const firePoint = weapon.firePoint;

        if (firePoint) {
            const fireWorldPos = firePoint.getWorldPosition();
            const weaponView = weapon.getView();

            console.group(`🎯 武器瞄准诊断`);
            console.log(`目标位置: (${targetPos.x.toFixed(3)}, ${targetPos.y.toFixed(3)}, ${targetPos.z.toFixed(3)})`);
            console.log(`武器逻辑位置: (${weaponLogicPos.x.toFixed(3)}, ${weaponLogicPos.y.toFixed(3)}, ${weaponLogicPos.z.toFixed(3)})`);
            console.log(`火力点世界位置: (${fireWorldPos.x.toFixed(3)}, ${fireWorldPos.y.toFixed(3)}, ${fireWorldPos.z.toFixed(3)})`);

            if (weaponView && weaponView.node) {
                const weaponViewWorldPos = weaponView.node.getWorldPosition();
                console.log(`武器视图世界位置: (${weaponViewWorldPos.x.toFixed(3)}, ${weaponViewWorldPos.y.toFixed(3)}, ${weaponViewWorldPos.z.toFixed(3)})`);

                const weaponPosDiff = this.calculateDistance(weaponLogicPos, {
                    x: weaponViewWorldPos.x,
                    y: weaponViewWorldPos.y,
                    z: weaponViewWorldPos.z
                });
                console.log(`武器位置差异: ${weaponPosDiff.toFixed(3)}`);
            }

            const targetDistance = this.calculateDistance({
                x: fireWorldPos.x,
                y: fireWorldPos.y,
                z: fireWorldPos.z
            }, targetPos);
            console.log(`目标距离: ${targetDistance.toFixed(3)}`);
            console.groupEnd();
            this.logCount++;
        }
    }

    /**
     * 诊断炮弹轨迹问题
     */
    public diagnoseBulletTrajectory(
        startPos: Vec3,
        targetPos: Vec3,
        currentPos: Vec3,
        velocity: Vec3,
        timeElapsed: number
    ): void {
        if (!this.isEnabled || this.logCount >= this.maxLogs) return;

        const distanceToTarget = this.calculateDistance({
            x: currentPos.x,
            y: currentPos.y,
            z: currentPos.z
        }, {
            x: targetPos.x,
            y: targetPos.y,
            z: targetPos.z
        });

        const totalDistance = this.calculateDistance({
            x: startPos.x,
            y: startPos.y,
            z: startPos.z
        }, {
            x: targetPos.x,
            y: targetPos.y,
            z: targetPos.z
        });

        const progress = totalDistance > 0 ? (totalDistance - distanceToTarget) / totalDistance : 0;

        console.group(`🚀 炮弹轨迹诊断`);
        console.log(`发射位置: (${startPos.x.toFixed(3)}, ${startPos.y.toFixed(3)}, ${startPos.z.toFixed(3)})`);
        console.log(`目标位置: (${targetPos.x.toFixed(3)}, ${targetPos.y.toFixed(3)}, ${targetPos.z.toFixed(3)})`);
        console.log(`当前位置: (${currentPos.x.toFixed(3)}, ${currentPos.y.toFixed(3)}, ${currentPos.z.toFixed(3)})`);
        console.log(`当前速度: (${velocity.x.toFixed(3)}, ${velocity.y.toFixed(3)}, ${velocity.z.toFixed(3)})`);
        console.log(`飞行时间: ${timeElapsed.toFixed(3)}秒`);
        console.log(`到目标距离: ${distanceToTarget.toFixed(3)}`);
        console.log(`总距离: ${totalDistance.toFixed(3)}`);
        console.log(`飞行进度: ${(progress * 100).toFixed(1)}%`);
        console.groupEnd();
        this.logCount++;
    }

    /**
     * 计算两点之间的距离
     */
    private calculateDistance(pos1: { x: number, y: number, z: number }, pos2: { x: number, y: number, z: number }): number {
        return Math.sqrt(
            Math.pow(pos2.x - pos1.x, 2) +
            Math.pow(pos2.y - pos1.y, 2) +
            Math.pow(pos2.z - pos1.z, 2)
        );
    }

    /**
     * 重置日志计数
     */
    public resetLogCount(): void {
        this.logCount = 0;
        console.log('位置诊断日志计数已重置');
    }

    /**
     * 获取当前状态
     */
    public getStatus(): { enabled: boolean, logCount: number, maxLogs: number } {
        return {
            enabled: this.isEnabled,
            logCount: this.logCount,
            maxLogs: this.maxLogs
        };
    }
}

// 全局访问接口
(window as any).PositionDiagnostics = PositionDiagnostics;
