import { _decorator } from 'cc';
import { IState } from '../core/StateMachine';
import { GameState } from './GameStateEnum';

// 前向声明 GameManager 类型，避免循环依赖
class GameManager {
    public changeState(state: GameState): void { }
    public gameOver(): void { }
}

/**
 * 游戏状态基类
 */
export abstract class BaseGameState implements IState {
    /** 状态名称 */
    public name: string = '';

    /** 游戏管理器引用 */
    protected gameManager: GameManager;

    /**
     * 构造函数
     * @param gameManager 游戏管理器引用
     */
    constructor(gameManager: GameManager) {
        this.gameManager = gameManager;
    }

    /**
     * 进入状态
     * @param prevState 上一个状态
     */
    public onEnter(prevState?: IState): void {
        console.log(`进入游戏状态: ${this.name}`);
    }

    /**
     * 退出状态
     * @param nextState 下一个状态
     */
    public onExit(nextState?: IState): void {
        console.log(`退出游戏状态: ${this.name}`);
    }

    /**
     * 更新状态
     * @param dt 时间增量
     */
    public onUpdate(dt: number): void {
        // 基类不做任何处理
    }

    /**
     * 检查是否可以转换到指定状态
     * @param targetState 目标状态
     * @returns 是否可以转换
     */
    public canTransitionTo(targetState: IState): boolean {
        // 默认允许所有状态转换
        return true;
    }
}

/**
 * 初始化状态
 */
export class InitState extends BaseGameState {
    constructor(gameManager: GameManager) {
        super(gameManager);
        this.name = GameState.INIT;
    }

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);

        // 执行游戏初始化逻辑
        console.log('游戏初始化中...');

        // 初始化完成后，自动切换到开始状态
        setTimeout(() => {
            this.gameManager.changeState(GameState.START);
        }, 1000);
    }
}

/**
 * 游戏开始状态
 */
export class StartState extends BaseGameState {
    constructor(gameManager: GameManager) {
        super(gameManager);
        this.name = GameState.START;
    }

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);

        // 显示游戏开始界面
        console.log('游戏准备开始，显示开始界面');

        // 在实际游戏中，这里可能会等待玩家点击"开始游戏"按钮
        // 为了演示，我们自动切换到游戏中状态
        setTimeout(() => {
            this.gameManager.changeState(GameState.PLAYING);
        }, 2000);
    }
}

/**
 * 游戏中状态
 */
export class PlayingState extends BaseGameState {
    constructor(gameManager: GameManager) {
        super(gameManager);
        this.name = GameState.PLAYING;
    }

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);

        // 开始游戏逻辑
        console.log('游戏开始，玩家可以控制游戏');
    }

    public onUpdate(dt: number): void {
        // 游戏主循环逻辑
        // 例如：检查游戏胜利或失败条件

        // 示例：假设游戏有一个时间限制，超过时间就游戏结束
        // if (this.gameManager.gameTime > 300) { // 5分钟
        //     this.gameManager.gameOver();
        // }
    }
}

/**
 * 游戏暂停状态
 */
export class PausedState extends BaseGameState {
    constructor(gameManager: GameManager) {
        super(gameManager);
        this.name = GameState.PAUSED;
    }

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);

        // 暂停游戏逻辑
        console.log('游戏暂停，显示暂停菜单');
    }
}

/**
 * 游戏结束状态
 */
export class GameOverState extends BaseGameState {
    constructor(gameManager: GameManager) {
        super(gameManager);
        this.name = GameState.GAME_OVER;
    }

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);

        // 游戏结束逻辑
        console.log('游戏结束，显示结束界面');
    }
}

/**
 * 游戏胜利状态
 */
export class GameWinState extends BaseGameState {
    constructor(gameManager: GameManager) {
        super(gameManager);
        this.name = GameState.GAME_OVER;
    }

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);

        // 游戏胜利逻辑
        console.log('游戏胜利，显示胜利界面');
    }
}
