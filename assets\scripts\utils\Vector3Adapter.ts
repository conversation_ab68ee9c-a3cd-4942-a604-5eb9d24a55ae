import { Vec3 } from 'cc';
import { Vector3, Vector3Util } from './Vector3';

/**
 * Vec3 和 Vector3 之间的适配器
 * 提供两种类型之间的转换方法
 */
export class Vector3Adapter {
    /**
     * 将 Vec3 转换为 Vector3
     * @param vec3 Cocos Creator 的 Vec3 对象
     * @returns 转换后的 Vector3 对象
     */
    public static fromVec3(vec3: Vec3): Vector3 {
        return { x: vec3.x, y: vec3.y, z: vec3.z };
    }

    /**
     * 将 Vector3 转换为 Vec3
     * @param vector3 Vector3 对象
     * @returns 转换后的 Vec3 对象
     */
    public static toVec3(vector3: Vector3): Vec3 {
        return new Vec3(vector3.x, vector3.y, vector3.z);
    }

    /**
     * 将 Vec3 数组转换为 Vector3 数组
     * @param vec3Array Cocos Creator 的 Vec3 对象数组
     * @returns 转换后的 Vector3 对象数组
     */
    public static fromVec3Array(vec3Array: Vec3[]): Vector3[] {
        return vec3Array.map(vec3 => Vector3Adapter.fromVec3(vec3));
    }

    /**
     * 将 Vector3 数组转换为 Vec3 数组
     * @param vector3Array Vector3 对象数组
     * @returns 转换后的 Vec3 对象数组
     */
    public static toVec3Array(vector3Array: Vector3[]): Vec3[] {
        return vector3Array.map(vector3 => Vector3Adapter.toVec3(vector3));
    }

    /**
     * 将 Vec3 的方法应用到 Vector3 上
     * @param method Vec3 的方法名
     * @param args 方法参数
     * @returns 方法返回值
     */
    public static applyVec3Method(method: keyof typeof Vec3, ...args: any[]): any {
        // 将 Vector3 参数转换为 Vec3
        const vec3Args = args.map(arg => {
            if (arg && typeof arg === 'object' && 'x' in arg && 'y' in arg && 'z' in arg) {
                return Vector3Adapter.toVec3(arg as Vector3);
            }
            return arg;
        });

        // 调用 Vec3 的方法
        const result = (Vec3[method] as Function)(...vec3Args);

        // 如果结果是 Vec3，转换为 Vector3
        if (result instanceof Vec3) {
            return Vector3Adapter.fromVec3(result);
        }

        return result;
    }

    /**
     * 计算两点之间的距离
     * @param a 点 a
     * @param b 点 b
     * @returns 距离
     */
    public static distance(a: Vector3, b: Vector3): number {
        const dx = b.x - a.x;
        const dy = b.y - a.y;
        const dz = b.z - a.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * 计算向量的长度
     * @param v 向量
     * @returns 长度
     */
    public static len(v: Vector3): number {
        return Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    }

    /**
     * 归一化向量
     * @param v 向量
     * @returns 归一化后的向量
     */
    public static normalize(v: Vector3): Vector3 {
        const len = Vector3Adapter.len(v);
        if (len > 0) {
            const invLen = 1 / len;
            return { x: v.x * invLen, y: v.y * invLen, z: v.z * invLen };
        }
        return { x: 0, y: 0, z: 0 };
    }

    /**
     * 向量点乘
     * @param a 向量 a
     * @param b 向量 b
     * @returns 点乘结果
     */
    public static dot(a: Vector3, b: Vector3): number {
        return a.x * b.x + a.y * b.y + a.z * b.z;
    }

    /**
     * 向量叉乘
     * @param a 向量 a
     * @param b 向量 b
     * @returns 叉乘结果
     */
    public static cross(a: Vector3, b: Vector3): Vector3 {
        return {
            x: a.y * b.z - a.z * b.y,
            y: a.z * b.x - a.x * b.z,
            z: a.x * b.y - a.y * b.x
        };
    }

    /**
     * 向量加法
     * @param a 向量 a
     * @param b 向量 b
     * @returns 加法结果
     */
    public static add(a: Vector3, b: Vector3): Vector3 {
        return { x: a.x + b.x, y: a.y + b.y, z: a.z + b.z };
    }

    /**
     * 向量减法
     * @param a 向量 a
     * @param b 向量 b
     * @returns 减法结果
     */
    public static subtract(a: Vector3, b: Vector3): Vector3 {
        return { x: a.x - b.x, y: a.y - b.y, z: a.z - b.z };
    }

    /**
     * 向量乘以标量
     * @param v 向量
     * @param s 标量
     * @returns 乘法结果
     */
    public static multiplyScalar(v: Vector3, s: number): Vector3 {
        return { x: v.x * s, y: v.y * s, z: v.z * s };
    }
}
