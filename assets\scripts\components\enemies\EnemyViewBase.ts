import { _decorator, Component, Node, Vec3, Animation, ParticleSystem, Color, tween, Sprite, UIOpacity } from 'cc';
import { EnemyBase } from '../../entities/enemies/EnemyBase';
import { <PERSON>rror<PERSON>anager, ErrorSeverity } from '../../managers/ErrorManager';
import { HealthBarUI } from '../ui/HealthBarUI';
const { ccclass, property } = _decorator;

@ccclass('EnemyViewBase')
export class EnemyViewBase extends Component {
    @property(Node)
    public healthBarNode: Node = null;

    @property(Node)
    public modelNode: Node = null;

    @property(Node)
    public hitEffectNode: Node = null;

    @property(ParticleSystem)
    public deathParticle: ParticleSystem = null;

    protected _logic: EnemyBase | null = null;
    protected _healthBar: HealthBarUI | null = null;
    protected _animation: Animation | null = null;
    protected _isPlayingHitAnimation: boolean = false;

    onLoad() {
        // 获取血条组件
        if (this.healthBarNode) {
            this._healthBar = this.healthBarNode.getComponent(HealthBarUI);
        }

        // 获取动画组件
        if (this.modelNode) {
            this._animation = this.modelNode.getComponent(Animation);
        }

        // 初始隐藏受击效果
        if (this.hitEffectNode) {
            this.hitEffectNode.active = false;
        }

        // 初始隐藏死亡粒子效果
        if (this.deathParticle) {
            this.deathParticle.stop();
            this.deathParticle.node.active = false;
        }
    }

    // 绑定逻辑对象
    public setLogic(enemy: EnemyBase) {
        this._logic = enemy;
        this.updatePosition(enemy.position);

        // 初始化血条
        this.updateHealthBar();
    }
      // 更新位置
    public updatePosition(position: { x: number, y: number, z: number }): void {
        try {
            if (this.node && this.node.isValid) {
                this.node.setPosition(new Vec3(position.x, position.y, position.z));
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `EnemyViewBase.updatePosition`
            );
        }
    }

    // 更新旋转
    public updateRotation(rotationDegrees: number): void {
        try {
            if (this.node && this.node.isValid) {
                this.node.setRotationFromEuler(0, rotationDegrees, 0);
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `EnemyViewBase.updateRotation`
            );
        }
    }

    /**
     * 更新血条显示
     */
    public updateHealthBar(): void {
        if (!this._logic || !this._healthBar) return;

        // 更新血条显示
        this._healthBar.updateHealth(this._logic.currentHealth, this._logic.maxHealth);

        // 如果血量为0，隐藏血条
        if (this._logic.currentHealth <= 0) {
            this._healthBar.forceHide();
        }
    }

    /**
     * 播放受击效果
     */
    public playHitEffect(): void {
        // 已经在播放受击动画，不重复播放
        if (this._isPlayingHitAnimation) return;

        this._isPlayingHitAnimation = true;

        // 显示受击特效
        if (this.hitEffectNode) {
            this.hitEffectNode.active = true;

            // 闪烁效果
            if (this.modelNode) {
                const sprite = this.modelNode.getComponent(Sprite);
                if (sprite) {
                    const originalColor = sprite.color.clone();
                    const hitColor = new Color(255, 100, 100, 255); // 红色闪烁

                    // 使用tween创建闪烁效果
                    tween(sprite)
                        .to(0.1, { color: hitColor })
                        .to(0.1, { color: originalColor })
                        .call(() => {
                            // 完成后隐藏受击特效
                            if (this.hitEffectNode) {
                                this.hitEffectNode.active = false;
                            }
                            this._isPlayingHitAnimation = false;
                        })
                        .start();
                } else {
                    // 如果没有Sprite组件，使用缩放效果代替
                    const originalScale = this.modelNode.scale.clone();
                    tween(this.modelNode)
                        .to(0.1, { scale: new Vec3(originalScale.x * 1.2, originalScale.y * 1.2, originalScale.z * 1.2) })
                        .to(0.1, { scale: originalScale })
                        .call(() => {
                            if (this.hitEffectNode) {
                                this.hitEffectNode.active = false;
                            }
                            this._isPlayingHitAnimation = false;
                        })
                        .start();
                }
            } else {
                // 如果没有模型节点，延迟隐藏受击特效
                setTimeout(() => {
                    if (this.hitEffectNode) {
                        this.hitEffectNode.active = false;
                    }
                    this._isPlayingHitAnimation = false;
                }, 200);
            }
        }

        // 播放受击动画
        if (this._animation) {
            this._animation.play('hit');
        }

        // 更新血条
        this.updateHealthBar();
    }

    /**
     * 播放死亡效果
     */
    public playDeathEffect(): void {
        // 播放死亡粒子效果
        if (this.deathParticle) {
            this.deathParticle.node.active = true;
            this.deathParticle.play();
        }

        // 播放死亡动画
        if (this._animation) {
            this._animation.play('death');
        }

        // 隐藏血条
        if (this._healthBar) {
            this._healthBar.forceHide();
        }

        // 隐藏模型（可选，取决于死亡效果设计）
        if (this.modelNode) {
            // 淡出效果
            const uiOpacity = this.modelNode.getComponent(UIOpacity);
            if (uiOpacity) {
                tween(uiOpacity)
                    .to(0.5, { opacity: 0 })
                    .start();
            } else {
                // 如果没有UIOpacity组件，直接设置不可见
                setTimeout(() => {
                    if (this.modelNode) {
                        this.modelNode.active = false;
                    }
                }, 500);
            }
        }
    }

    /**
     * 每帧更新
     */
    public updateView(_deltaTime: number): void {
        // 更新血条位置（如果需要）
        if (this._healthBar && this._logic && this._logic.isAlive) {
            // 可以在这里添加血条跟随逻辑
        }
    }

    /**
     * 处理敌人死亡
     */
    public onDead(): void {
        // 播放死亡效果
        this.playDeathEffect();

        // 延迟移除节点（让死亡动画有时间播放）
        setTimeout(() => {
            if (this.node && this.node.isValid) {
                this.node.removeFromParent();
            }
        }, 2000); // 增加时间以确保死亡效果完全播放
    }    /**
     * 恢复方法 - 用于在错误发生后重置视图状态
     * @returns 恢复是否成功
     */
    public recover(): boolean {
        try {
            // 如果有逻辑组件，重置位置和旋转
            if (this._logic) {
                this.updatePosition(this._logic.position);
                // 默认重置为面向前方
                this.updateRotation(0);

                ErrorManager.getInstance().logInfo(
                    '敌人视图已恢复',
                    'EnemyViewBase.recover'
                );
                return true;
            }
            return false;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.recover'
            );
            return false;
        }
    }

    /**
     * 从错误中恢复
     * @param errorContext 错误上下文
     * @param severity 错误严重程度
     */
    public recoverFromError(errorContext: string, _severity: ErrorSeverity): boolean {
        try {
            // 根据错误上下文选择适当的恢复策略
            if (errorContext.includes('updatePosition')) {
                // 位置更新错误
                if (this._logic) {
                    this.resetPosition();
                    return true;
                }
            }
            else if (errorContext.includes('updateRotation')) {
                // 旋转更新错误
                this.resetRotation();
                return true;
            }
            else if (errorContext.includes('updateView')) {
                // 视图更新通用错误
                this.recover();
                return true;
            }

            // 默认恢复策略
            return false;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.recoverFromError'
            );
            return false;
        }
    }

    /**
     * 重置位置
     */
    private resetPosition(): void {
        try {
            if (this._logic && this.node && this.node.isValid) {
                this.node.setPosition(
                    new Vec3(this._logic.position.x, this._logic.position.y, this._logic.position.z)
                );

                ErrorManager.getInstance().logInfo(
                    '敌人位置已重置',
                    'EnemyViewBase.resetPosition'
                );
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.resetPosition'
            );
        }
    }

    /**
     * 重置旋转
     */
    private resetRotation(): void {
        try {
            if (this.node && this.node.isValid) {
                this.node.setRotationFromEuler(0, 0, 0);

                ErrorManager.getInstance().logInfo(
                    '敌人旋转已重置',
                    'EnemyViewBase.resetRotation'
                );
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.resetRotation'
            );
        }
    }
}
