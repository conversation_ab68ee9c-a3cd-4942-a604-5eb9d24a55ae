import { _decorator, Component, Node, Vec3 } from 'cc';
import { EnemyBase } from '../../entities/enemies/EnemyBase';
import { Error<PERSON>anager, ErrorSeverity } from '../../managers/ErrorManager';
const { ccclass, property } = _decorator;

@ccclass('EnemyViewBase')
export class EnemyViewBase extends Component {

    protected _logic: EnemyBase | null = null;

    // 绑定逻辑对象
    public setLogic(enemy: EnemyBase) {
        this._logic = enemy;
        this.updatePosition(enemy.position);
    }
      // 更新位置
    public updatePosition(position: { x: number, y: number, z: number }): void {
        try {
            if (this.node && this.node.isValid) {
                this.node.setPosition(new Vec3(position.x, position.y, position.z));
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `EnemyViewBase.updatePosition`
            );
        }
    }

    // 更新旋转
    public updateRotation(rotationDegrees: number): void {
        try {
            if (this.node && this.node.isValid) {
                this.node.setRotationFromEuler(0, rotationDegrees, 0);
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `EnemyViewBase.updateRotation`
            );
        }
    }

    // 这里可以定义通用的敌人表现逻辑，如受击、死亡动画等
    public playHitEffect(): void {
        // 播放受击特效
//         console.log('播放受击特效');
    }

    public playDeathEffect(): void {
        // 播放死亡特效
//         console.log('播放死亡特效');
    }

    public updateView(dt: number): void {
        // 可在这里实现表现层的每帧刷新逻辑
    }

    public onDead(): void {
        // 播放死亡效果
        this.playDeathEffect();

        // 延迟移除节点（让死亡动画有时间播放）
        setTimeout(() => {
            if (this.node && this.node.isValid) {
                this.node.removeFromParent();
            }
        }, 1000);
    }    /**
     * 恢复方法 - 用于在错误发生后重置视图状态
     * @returns 恢复是否成功
     */
    public recover(): boolean {
        try {
            // 如果有逻辑组件，重置位置和旋转
            if (this._logic) {
                this.updatePosition(this._logic.position);
                // 默认重置为面向前方
                this.updateRotation(0);

                ErrorManager.getInstance().logInfo(
                    '敌人视图已恢复',
                    'EnemyViewBase.recover'
                );
                return true;
            }
            return false;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.recover'
            );
            return false;
        }
    }

    /**
     * 从错误中恢复
     * @param errorContext 错误上下文
     * @param severity 错误严重程度
     */
    public recoverFromError(errorContext: string, severity: ErrorSeverity): boolean {
        try {
            // 根据错误上下文选择适当的恢复策略
            if (errorContext.includes('updatePosition')) {
                // 位置更新错误
                if (this._logic) {
                    this.resetPosition();
                    return true;
                }
            }
            else if (errorContext.includes('updateRotation')) {
                // 旋转更新错误
                this.resetRotation();
                return true;
            }
            else if (errorContext.includes('updateView')) {
                // 视图更新通用错误
                this.recover();
                return true;
            }

            // 默认恢复策略
            return false;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.recoverFromError'
            );
            return false;
        }
    }

    /**
     * 重置位置
     */
    private resetPosition(): void {
        try {
            if (this._logic && this.node && this.node.isValid) {
                this.node.setPosition(
                    new Vec3(this._logic.position.x, this._logic.position.y, this._logic.position.z)
                );

                ErrorManager.getInstance().logInfo(
                    '敌人位置已重置',
                    'EnemyViewBase.resetPosition'
                );
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.resetPosition'
            );
        }
    }

    /**
     * 重置旋转
     */
    private resetRotation(): void {
        try {
            if (this.node && this.node.isValid) {
                this.node.setRotationFromEuler(0, 0, 0);

                ErrorManager.getInstance().logInfo(
                    '敌人旋转已重置',
                    'EnemyViewBase.resetRotation'
                );
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'EnemyViewBase.resetRotation'
            );
        }
    }
}
