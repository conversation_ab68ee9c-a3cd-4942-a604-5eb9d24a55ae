import { Vec3, Prefab, Node, geometry, PhysicsSystem, Quat, ICollisionEvent } from 'cc';
// 暂时注释掉这些导入，以便编译通过
// import { ProjectileCalculator } from '../../core/ProjectileCalculator';
// import { EffectManager } from '../../managers/EffectManager';

import { MathUtil } from '../../utils/MathUtil';
import { BulletViewBase } from '../../components/projectiles/BulletViewBase';
import { Inject } from '../../decorators/Inject';
import { IGameManager } from '../../interfaces/IManager';
import { StateMachine } from '../../core/StateMachine';
import { ProjectileIdleState, ProjectileActiveState, ProjectileCollidedState, ProjectileDestroyedState } from './ProjectileStates';
import { ProjectileState } from '../../enums/ProjectileState';

/**
 * 抛射物模式
 */
export enum ProjectileMode {
    /** 物理模式：完整的物理交互 */
    PHYSICS = 1,
    /** 数学模式：纯数学计算 */
    MATH = 2,
}

/**
 * 碰撞检测模式
 */
export enum CollisionDetectionMode {
    /** 不检测碰撞（仅检测地面） */
    NONE = 0,
    /** 射线检测 */
    RAY_CAST = 1,
    /** 球体检测 */
    SPHERE_CAST = 2,
}

/**
 * 抛射物核心类
 * 为各种抛射物（如子弹、炮弹、箭等）提供基础功能
 * 不继承自 Component，而是作为一个独立的逻辑类
 */
export abstract class ProjectileCore {

    @Inject('gameManager')
    private _gameManager: IGameManager = null;


    /**
      * 累积器，用于时间缩放
      */
    protected accumulator: number = 0;


    // 临时数组，用于存储向量计算结果，避免创建新的 Vec3 对象
    protected tempVec: number[] = [0, 0, 0];
    protected tempDirection: number[] = [0, 0, 0];

    // 最近一次碰撞信息（仍然需要Vec3对象用于传递给其他API）
    protected lastHitPoint: Vec3 = new Vec3(); // 碰撞点
    protected lastHitNormal: Vec3 = new Vec3(); // 碰撞法线

    // 临时Vec3对象，用于避免在方法中创建新的Vec3
    protected tempVec3_1: Vec3 = new Vec3();
    protected tempVec3_2: Vec3 = new Vec3();
    // 用于旋转计算
    protected tempRotation: Quat = new Quat();
    protected lastPosition: Vec3 = new Vec3(); // 上一帧位置，用于射线检测和旋转计算
    protected curPosition: Vec3 = new Vec3();  // 当前位置
    protected currentVelocity: Vec3 = new Vec3();

    // 碰撞检测相关属性
    protected collisionDetectionMode: CollisionDetectionMode = CollisionDetectionMode.NONE;
    protected sphereRadius: number = 1.0; // 球体检测的半径
    protected rayLength: number = 1.0; // 射线检测的长度
    /**
* 前进方向（数学模式使用）
*/
    protected forwardDirection: Vec3 = new Vec3();
    /**
     * 是否已被触发
     */
    protected triggered: boolean = false;

    /**
     * 抛射物模式
     */
    protected _mode: ProjectileMode = ProjectileMode.PHYSICS;
    public get mode(): ProjectileMode {
        return this._mode;
    }
    protected set mode(value: ProjectileMode) {
        this._mode = value;
    }

    /**
     * 碰撞检测模式
     */
    protected _collisionMode: CollisionDetectionMode = CollisionDetectionMode.NONE;
    public get collisionMode(): CollisionDetectionMode {
        return this._collisionMode;
    }
    protected set collisionMode(value: CollisionDetectionMode) {
        this._collisionMode = value;
    }

    /**
     * 初始位置
     */
    protected _initialPosition: Vec3 = new Vec3();

    public get initialPosition() {
        return this._initialPosition
    }

    protected _firePosition: Vec3 = new Vec3();
    /**
     * 目标位置
     */
    protected targetPosition: Vec3 = new Vec3();

    /**
     * 当前位置
     */
    protected _position: Vec3 = new Vec3();
    public get position(): Vec3 {
        return this._position;
    }
    protected _rotation: Quat = new Quat();;
    public get rotation(): Quat {
        return this._rotation;
    }
    /**
     * 初始速度
     */
    protected initialVelocity: number = 0;

    /**
     * 初始角度
     */
    protected initialAngle: number = 0;

    /**
     * 发射时间
     */
    protected fireTime: number = 0;

    /**
     * 飞行时间
     */
    protected flightTime: number = 0;

    /**
     * 时间缩放
     */
    protected timeScale: number = 1.0;

    /**
     * 临时变量，用于记录开始时间
     */
    public tempStartTime: number = 0;



    /**
     * 启用旋转
     */
    protected enableRotation: boolean = true;

    /**
     * 是否活跃
     */
    protected _isActive: boolean = true;
    public get isActive(): boolean {
        return this._isActive;
    }

    /**
     * 视图组件
     */
    protected _view: BulletViewBase = null;
    public get view(): BulletViewBase {
        return this._view;
    }
    get firePosition() {
        return this._firePosition;
    }    /**
     * 构造函数
     * @param prefab 预制体
     * @param parent 父节点
     */
    constructor(prefab: Prefab, parent: Node) {
        // 创建视图
        this._view = this.createView(prefab, parent);

        // 初始化状态机
        this._stateMachine = this.createStateMachine();
    }

    /**
     * 创建视图
     * 子类需要实现此方法，创建特定的视图组件
     * @param prefab 预制体
     * @param parent 父节点
     */
    protected abstract createView(prefab: Prefab, parent: Node): BulletViewBase;

    /**
        * 初始化数学模式
        * @param angle 初始角度
        * @param velocity 初始速度
        * @param forward 前进方向
        * @param targetPos 目标位置（可选）
        * @param collisionMode 碰撞检测模式（可选，默认为 RAY_CAST）
        * @param sphereRadius 球体检测的半径（可选，仅在球体检测模式下有效）
        * @param rayLength 射线检测的长度（可选，仅在射线检测模式下有效）
        * @param timeScale 时间缩放因子（可选，默认为1.0，值越大炮弹移动越快）
        */
    public initMathMode(
        angle: number,
        velocity: number,
        forward: { x: number, y: number, z: number },
        startPos: Vec3,
        targetPos?: Vec3,
        collisionMode: CollisionDetectionMode = CollisionDetectionMode.RAY_CAST,
        sphereRadius?: number,
        rayLength: number = 0.5,
        timeScale: number = 1.0
    ) {
        this._isActive = true;
        this.mode = ProjectileMode.MATH;
        this.initialAngle = angle;
        this.initialVelocity = velocity;
        this.timeScale = timeScale;
        this.fireTime = 0; // 重置发射时间
        this.flightTime = 0; // 重置飞行时间
        this._initialPosition.set(startPos)
        this._firePosition.set(startPos);
        this.triggered = false;

        this.setCollisionDetectionMode(collisionMode, sphereRadius, rayLength);
        // 禁用物理组件
        if (this._view?.rigidBody) {
            this._view.rigidBody.enabled = false;
        }

        // 如果提供了目标位置，则使用从发射点到目标点的方向
        if (targetPos) {
            // 保存目标位置，用于计算轨迹
            this.targetPosition.set(targetPos);

            // 从发射点指向目标点的方向矢量 (使用 MathUtil 避免创建 Vec3)
            const dirX = targetPos.x - this._initialPosition.x;
            const dirY = targetPos.y - this._initialPosition.y;
            const dirZ = targetPos.z - this._initialPosition.z;
            // 将方向矢量投影到水平面上 (使用 MathUtil 的优化版本)
            MathUtil.projectOnHorizontalPlane(dirX, dirY, dirZ, this.tempDirection);
            // 归一化方向向量
            MathUtil.normalize(this.tempDirection[0], this.tempDirection[1], this.tempDirection[2], this.tempDirection);

            // 设置前进方向
            this.forwardDirection.set(this.tempDirection[0], this.tempDirection[1], this.tempDirection[2]);
        } else {
            // 否则使用传入的方向
            this.forwardDirection.set(forward.x, forward.y, forward.z).normalize();

            // 计算一个假想的目标位置（沿着前进方向延伸1000个单位）
            this.targetPosition.set(
                this._initialPosition.x + this.forwardDirection.x * 1000,
                this._initialPosition.y + this.forwardDirection.y * 1000,
                this._initialPosition.z + this.forwardDirection.z * 1000
            );
        }
        this._view.reset();

        // 如果当前是销毁状态，先强制重置状态机
        if (this.stateName === 'destroyed') {
            // console.log(`ProjectileCore: 从销毁状态强制重置状态机`);
            (this._stateMachine as any).resetToInitialState();
        }

        // 切换到活跃状态
        this.changeState('active');
    }
    /**
     * 设置碰撞检测模式
     * @param mode 碰撞检测模式
     * @param sphereRadius 球体检测的半径（可选，仅在球体检测模式下有效）
     * @param rayLength 射线检测的长度（可选，仅在射线检测模式下有效）
     */
    protected setCollisionDetectionMode(
        mode: CollisionDetectionMode,
        sphereRadius?: number,
        rayLength?: number
    ) {


        // 保存碰撞检测模式
        this.collisionDetectionMode = mode;

        // 根据碰撞检测模式设置参数
        switch (mode) {
            case CollisionDetectionMode.SPHERE_CAST:
                // 设置球体检测半径
                if (sphereRadius !== undefined) {
                    this.sphereRadius = sphereRadius;
                }
                break;
            case CollisionDetectionMode.RAY_CAST:
                // 设置射线检测长度
                if (rayLength !== undefined) {
                    this.rayLength = rayLength;
                }
                break;
        }
    }

    /**
     * 根据速度更新旋转
     * @param velocity 速度向量
     */
    protected updateRotationFromVelocity(_velocity: Vec3) {
        // 子类实现旋转逻辑
    }




    /**
     * 执行射线检测
     * @param from 射线起点
     * @param to 射线终点
     * @returns 是否检测到碰撞
     */
    protected raycastDetection(from: Vec3, to: Vec3): boolean {
        // 计算方向 - 使用预分配的Vec3对象
        Vec3.subtract(this.tempVec3_1, to, from);

        const ray = geometry.Ray.create();
        // 创建射线
        geometry.Ray.fromPoints(ray, from, to);
        const phy = PhysicsSystem.instance;

        // 执行射线检测，使用有效射线长度
        if (phy.raycastClosest(ray, 0xffffff, this.rayLength)) {
            const raycastResult = phy.raycastClosestResult;

            // 如果检测到碰撞，并且不是自己，且不是地面
            if (raycastResult.collider &&
                raycastResult.collider.node !== this._view.node &&
                raycastResult.hitPoint.y > 0.01) { // 忽略地面碰撞

                // 检测到碰撞

                // 保存碰撞点和法线，用于后续处理 - 避免使用clone()
                this.lastHitPoint.set(raycastResult.hitPoint);
                this.lastHitNormal.set(raycastResult.hitNormal);

                return true;
            }
        }

        return false;
    }    /**
     * 更新
     * @param dt 时间增量
     */
    public update(deltaTime: number) {
        if (!this._isActive) return;

        // 使用状态机更新
        if (this._stateMachine) {
            this._stateMachine.update(deltaTime);
        }

        // 更新视图
        if (this._view) {
            this._view.updateView(deltaTime);
        }
    }
    protected updateMathPosition(_dt: number) {
        // 子类实现具体的数学模式位置更新
    }
    /**
     * 初始化物理模式
     * @param velocity 初始速度向量
     * @param startPos 初始位置
     * @param startRotation 初始旋转
     * @param collisionMode 碰撞检测模式
     * @param sphereRadius 球体检测的半径（可选，仅在球体检测模式下有效）
     * @param rayLength 射线检测的长度（可选，仅在射线检测模式下有效）
     * @param timeScale 时间缩放因子（可选，默认为1.0，值越大抛射物移动越快）
     */
    public initPhysicsMode(
        velocity: Vec3,
        startPos: Vec3,
        startRotation: Quat,
        collisionMode: CollisionDetectionMode = CollisionDetectionMode.NONE,
        sphereRadius?: number,
        rayLength?: number,
        timeScale: number = 1.0
    ) {
        // 设置为物理模式
        this.mode = ProjectileMode.PHYSICS;

        // 设置时间缩放
        this.timeScale = timeScale;
        this._isActive = true;
        // 重置状态
        this.triggered = false;
        this.fireTime = 0; // 重置发射时间
        this.flightTime = 0; // 重置飞行时间
        this._initialPosition.set(startPos)
        this._firePosition.set(startPos);
        this._view.node.setWorldPosition(startPos);
        this._view.node.setWorldRotation(startRotation);
        // 保存初始位置，用于后续计算

        this.lastPosition.set(this._initialPosition);

        // 设置碰撞检测模式
        this.setCollisionDetectionMode(collisionMode, sphereRadius, rayLength);

        // 确保刚体和碰撞器已启用
        if (this._view.rigidBody) {
            this._view.rigidBody.enabled = true;
            // 设置初始速度
            this._view.rigidBody.setLinearVelocity(velocity);
            // 保存初始速度大小，用于后续计算
            this.initialVelocity = Vec3.len(velocity);
        }

        if (this._view.collider) {
            this._view.collider.enabled = true;
            this._view.collider.on('onCollisionEnter', this.onCollisionEnter, this);
        }

        // 更新旋转，使抛射物朝向运动方向
        if (this.enableRotation && Vec3.lengthSqr(velocity) > 0.0001) {
            this.updateRotationFromVelocity(velocity);
        }
        this._view.reset();

        // 如果当前是销毁状态，先强制重置状态机
        if (this.stateName === 'destroyed') {
            // console.log(`ProjectileCore: 从销毁状态强制重置状态机`);
            (this._stateMachine as any).resetToInitialState();
        }

        // 切换到活跃状态
        this.changeState('active');
    }
    protected abstract onCollisionEnter(event: ICollisionEvent): void;
    /**
      * 更新物理模式下的旋转
      */
    protected updatePhysicsRotation() {
        // 获取当前位置
        this._view.node.getWorldPosition(this.curPosition);

        // 计算速度向量（当前位置 - 上一帧位置）
        Vec3.subtract(this.currentVelocity, this.curPosition, this.lastPosition);

        // 如果速度太小，不更新旋转
        if (Vec3.lengthSqr(this.currentVelocity) < 0.0001) {
            return;
        }

        // 更新旋转，使炮弹朝向运动方向
        this.updateRotationFromVelocity(this.currentVelocity);

        // 保存当前位置用于下一帧计算
        this.lastPosition.set(this.curPosition);
    }    /**
     * 销毁抛射物
     * @param skipRecycle 是否跳过回收到对象池（用于避免循环调用）
     */
    public destroy(skipRecycle: boolean = false) {
        if (!this._isActive) return;

        this._isActive = false;

        if (this._view) {
            this._view.cleanup();

            if (this._view.collider) {
                this._view.collider.off('onCollisionEnter', this.onCollisionEnter, this);
            }
        }

        // 只有在不跳过回收的情况下才调用回收方法
        if (!skipRecycle) {
            // 优先使用缓存的 GameManager 实例，避免触发 getter
            // console.log(`ProjectileCore: 销毁投射物，回收到对象池`);
            this._gameManager.removeProjectile(this);
        }
    }

    /**
     * 投射物状态机
     */
    protected _stateMachine: StateMachine;

    /**
     * 获取当前状态名称
     */
    public get stateName(): string {
        return this._stateMachine ? this._stateMachine.stateName : 'none';
    }

    /**
     * 切换状态
     */
    public changeState(stateName: string): boolean {
        if (this._stateMachine) {
            return this._stateMachine.changeState(stateName);
        }
        return false;
    }

    /**
     * 碰撞触发方法，供状态机使用
     */
    public onCollision(_position?: Vec3, _normal?: Vec3): void {
        // 子类实现具体碰撞逻辑
    }

    /**
     * 执行销毁，供状态机使用
     */
    public performDestroy(): void {
        this.destroy(true);
    }

    /**
     * 更新投射物状态，根据当前模式选择不同的更新方法
     */
    public updateProjectile(dt: number): void {
        if (this.mode === ProjectileMode.MATH) {
            this.updateMathPosition(dt);
        } else {
            this.updatePhysicsMode(dt);
        }
    }

    /**
     * 物理模式下的更新
     */
    protected updatePhysicsMode(dt: number): void {
        // 累加飞行时间
        this.flightTime += dt;

        if (this.timeScale !== 1.0) {
            // 累积时间
            this.accumulator += dt * this.timeScale;

            while (this.accumulator >= dt) {
                // 获取当前状态
                this._view.rigidBody.getLinearVelocity(this.currentVelocity);
                this._view.node.getWorldPosition(this.curPosition);

                const gravity = PhysicsSystem.instance.gravity;

                // 更新速度（考虑重力）
                Vec3.multiplyScalar(this.tempVec3_1, gravity, dt);
                Vec3.add(this.currentVelocity, this.currentVelocity, this.tempVec3_1);

                // 更新位置
                Vec3.multiplyScalar(this.tempVec3_2, this.currentVelocity, dt);
                Vec3.add(this.curPosition, this.curPosition, this.tempVec3_2);

                // 应用新的状态
                this._view.node.setWorldPosition(this.curPosition);
                this._view.rigidBody.setLinearVelocity(this.currentVelocity);

                // 减少累积时间
                this.accumulator -= dt;
            }
        }

        if (this.enableRotation) {
            this.updatePhysicsRotation();
        }
    }

    /**
     * 初始化并创建状态机
     */
    protected createStateMachine(): StateMachine {
        const sm = new StateMachine();

        // 使用 registerState 注册状态（与 Game 状态机保持一致）
        sm.registerState(ProjectileState.IDLE, new ProjectileIdleState(this), true); // 默认为空闲状态
        sm.registerState(ProjectileState.ACTIVE, new ProjectileActiveState(this));
        sm.registerState(ProjectileState.COLLIDED, new ProjectileCollidedState(this));
        sm.registerState(ProjectileState.DESTROYED, new ProjectileDestroyedState(this));

        // 设置状态转换表（更直观的方式）
        sm.setTransitions(new Map([
            [ProjectileState.IDLE, [ProjectileState.ACTIVE, ProjectileState.DESTROYED]],
            [ProjectileState.ACTIVE, [ProjectileState.COLLIDED, ProjectileState.DESTROYED]],
            [ProjectileState.COLLIDED, [ProjectileState.DESTROYED]],
            [ProjectileState.DESTROYED, [ProjectileState.IDLE, ProjectileState.ACTIVE]] // 支持对象池重用
        ]));

        return sm;
    }
}
