import { Vec3, Prefab, Node, Quat, instantiate, ICollisionEvent } from 'cc';
import { ProjectileCore, CollisionDetectionMode } from './ProjectileCore';
import { ProjectileCalculator } from '../../core/ProjectileCalculator';
import { MathUtil } from '../../utils/MathUtil';
import { BulletViewBase } from '../../components/projectiles/BulletViewBase';
import { Inject } from '../../decorators/Inject';
import { EffectManager } from '../../managers/EffectManager';
import { StateMachine } from '../../core/StateMachine';
import { BulletExplodingState } from './ProjectileStates';
import { EnemyViewBase } from '../../components/enemies/EnemyViewBase';
import { ProjectileState } from '../../enums/ProjectileState';

/**
 * 子弹核心类
 * 继承自抛射物核心类，实现子弹特有的功能
 */
export class BulletCore extends ProjectileCore {

    /** 效果管理器（依赖注入） */
    @Inject('effectManager')
    private _effectManager: EffectManager;

    /**
     * 是否触发爆炸（静态变量，所有实例共享）
     */
    public static triggerExplode: boolean = true;

    /**
     * 当前实例是否触发爆炸（实例变量，每个实例独立）
     * 在每一帧更新时从静态变量同步
     */
    protected instanceTriggerExplode: boolean = true;

    /**
     * 最大生命周期（秒）
     */
    protected maxLifetime: number = 10;

    /**
     * 前进方向
     */
    protected forwardDirection: Vec3 = new Vec3();

    /**
     * 目标位置（用于计算轨迹）
     */
    protected targetPos: Vec3 = new Vec3();

    /**
     * 伤害值
     */
    protected damage: number = 50;

    /**
     * 是否有爆炸效果
     */
    protected hasExplosive: boolean = false;

    /**
     * 穿透次数
     */
    protected pierceCount: number = 1;

    /**
     * 构造函数
     * @param prefab 预制体
     * @param parent 父节点
     * @param damage 伤害值
     * @param maxLifetime 最大生命周期
     * @param hasExplosive 是否有爆炸效果
     * @param pierceCount 穿透次数
     */
    constructor(
        prefab: Prefab,
        parent: Node,
        damage: number = 50,
        maxLifetime: number = 10,
        hasExplosive: boolean = false,
        pierceCount: number = 1
    ) {
        super(prefab, parent);

        this.damage = damage;
        this.maxLifetime = maxLifetime;
        this.hasExplosive = hasExplosive;
        this.pierceCount = pierceCount;
    }

    /**
     * 创建视图
     * @param prefab 预制体
     * @param parent 父节点
     */
    protected createView(prefab: Prefab, parent: Node): BulletViewBase {
        if (this._view) return this._view;
        // 实例化预制体
        const node = instantiate(prefab);
        parent.addChild(node);
        // 获取或添加 ProjectileView 组件
        let view = node.getComponent(BulletViewBase);
        // 初始化视图
        view.setLogic(this);
        return view;
    }

    /**
     * 重写创建状态机方法，添加子弹特有状态
     */
    protected createStateMachine(): StateMachine {
        const sm = super.createStateMachine();

        // 使用 registerState 添加子弹特有的爆炸状态
        sm.registerState(ProjectileState.EXPLODING, new BulletExplodingState(this));

        // 重新设置状态转换表，包含爆炸状态
        sm.setTransitions(new Map([
            [ProjectileState.IDLE, [ProjectileState.ACTIVE, ProjectileState.DESTROYED]],
            [ProjectileState.ACTIVE, [ProjectileState.EXPLODING, ProjectileState.COLLIDED, ProjectileState.DESTROYED]],
            [ProjectileState.EXPLODING, [ProjectileState.DESTROYED]],
            [ProjectileState.COLLIDED, [ProjectileState.DESTROYED]],
            [ProjectileState.DESTROYED, [ProjectileState.IDLE, ProjectileState.ACTIVE]] // 支持对象池重用
        ]));

        return sm;
    }

    /**
     * 更新数学模式下的位置
     * @param dt 帧间隔时间（秒）
     */
    protected updateMathPosition(dt: number) {
        if (!this.isActive) return;

        // 累加飞行时间
        this.flightTime += dt * this.timeScale;

        // 检查生命周期
        if (this.flightTime / this.timeScale > this.maxLifetime) {
            this.changeState(ProjectileState.DESTROYED);
            return;
        }

        // 保存上一帧位置，用于射线检测
        this.lastPosition.set(this._position.x, this._position.y, this._position.z);

        // 计算当前位移
        const displacement = ProjectileCalculator.calculateDisplacementAtMoment(
            this.initialAngle,
            this.initialVelocity,
            this.flightTime
        );

        // 计算新位置（使用 MathUtil 避免创建 Vec3 对象）
        // 初始位置

        // 计算水平位移 - 使用归一化的方向向量
        // 先归一化方向向量
        MathUtil.normalize(
            this.forwardDirection.x,
            this.forwardDirection.y,
            this.forwardDirection.z,
            this.tempDirection
        );

        // 计算水平位移 (存储在 tempVec 中)
        this.tempVec[0] = this.tempDirection[0] * displacement.x;
        this.tempVec[1] = this.tempDirection[1] * displacement.x;
        this.tempVec[2] = this.tempDirection[2] * displacement.x;

        // 应用水平和垂直位移
        this._position.set(
            this.firePosition.x + this.tempVec[0],
            this.firePosition.y + this.tempVec[1] + displacement.y,
            this.firePosition.z + this.tempVec[2]
        );

        // 如果启用旋转，计算当前速度方向并更新旋转
        if (this.enableRotation) {
            // 计算当前速度向量
            // 水平方向 = 归一化的前进方向
            const vx = this.tempDirection[0];
            const vz = this.tempDirection[2];

            // 垂直方向 = 当前时刻的垂直速度
            const velocityAtMoment = ProjectileCalculator.calculateVelocityAtMoment(
                this.initialAngle,
                this.initialVelocity,
                this.flightTime
            );
            const verticalVelocity = velocityAtMoment.vy;

            // 合成速度向量
            this.currentVelocity.set(
                vx * this.initialVelocity * Math.cos(this.initialAngle * MathUtil.deg2Rad),
                verticalVelocity,
                vz * this.initialVelocity * Math.cos(this.initialAngle * MathUtil.deg2Rad)
            );

            // 更新旋转
            this.updateRotationFromVelocity(this.currentVelocity);
        }

        // 执行碰撞检测
        let hasCollision = false;

        // 检查是否碰到地面
        let hasGroundCollision = false;

        if (this._position.y <= 0) {
            // 防止穿透地面
            this._position.y = 0;
            hasCollision = true;
            hasGroundCollision = true;

            // 记录碰撞点信息，用于后续处理 - 使用预分配的Vec3对象
            this.lastHitPoint.set(this._position.x, 0, this._position.z); // 地面碰撞点
            this.lastHitNormal.set(0, 1, 0);  // 地面法线向上
        }

        // 根据碰撞检测模式执行不同的检测
        if (!hasCollision) {
            // 射线检测：从上一帧位置到当前位置
            hasCollision = this.raycastDetection(this.lastPosition, this._position);

            if (hasCollision) {
                // console.log("射线检测到碰撞！");
            }
        }

        // 如果检测到碰撞且尚未触发
        if (hasCollision && !this.triggered) {
            // 使用统一的碰撞处理方法
            if (hasGroundCollision) {
                // 地面碰撞
                this.handleCollision(this.lastHitPoint, this.lastHitNormal, 'math');
            } else if (this.collisionDetectionMode !== CollisionDetectionMode.NONE) {
                // 射线或球体检测到的碰撞
                this.handleCollision(this.lastHitPoint, this.lastHitNormal, 'math');
            } else {
                // 其他情况，使用当前位置
                this.handleCollision(this._position, undefined, 'math');
            }
        }
    }

    /**
     * 物理碰撞回调：开始
     * @param event 碰撞事件
     */
    protected onCollisionEnter(event: ICollisionEvent) {
        // 防止重复触发
        if (this.triggered) {
            return;
        }

        // 获取碰撞点和法线
        const contact = event.contacts[0];
        let worldPoint: Vec3 | undefined;
        let normal: Vec3 | undefined;

        if (contact) {
            worldPoint = new Vec3();
            normal = new Vec3();
            // 使用接触方程获取碰撞点和法线
            contact.getWorldPointOnA(worldPoint);
            contact.getWorldNormalOnA(normal);
        } else {
            // 如果没有接触点，使用当前位置
            worldPoint = this._view.node.getWorldPosition();
        }

        // 检查碰撞对象是否是敌人并处理伤害
        this.handleEnemyDamage(event);

        // 统一处理碰撞
        this.handleCollision(worldPoint, normal, 'physics');
    }

    /**
     * 处理敌人伤害
     * @param event 碰撞事件
     */
    private handleEnemyDamage(event: ICollisionEvent): void {
        // 检查碰撞对象是否是敌人
        const otherCollider = event.otherCollider;
        if (otherCollider && otherCollider.node) {
            // 尝试获取敌人组件
            const enemyView = otherCollider.node.getComponent(EnemyViewBase);
            if (enemyView) {
                // 获取敌人逻辑对象
                const enemy = enemyView['_logic']; // 直接访问私有属性
                if (enemy && typeof enemy.takeDamage === 'function') {
                    // 对敌人造成伤害
                    const damage = this.damage || 10; // 使用炮弹的伤害值，如果没有则默认为10
                    console.log(`炮弹击中敌人 ${enemy.id}，造成 ${damage} 点伤害`);
                    enemy.takeDamage(damage);
                }
            }
        }
    }

    /**
     * 统一的碰撞处理方法
     * @param position 碰撞位置
     * @param normal 碰撞法线
     * @param source 碰撞来源：'physics' 或 'math'
     */
    private handleCollision(position?: Vec3, normal?: Vec3, source: 'physics' | 'math' = 'math'): void {
        // 防止重复触发
        if (this.triggered) {
            return;
        }

        this.triggered = true;

        // 输出调试信息
        console.log(`${source === 'physics' ? '物理' : '数学'}模式检测到碰撞！爆炸设置：${this.instanceTriggerExplode}`);

        // 调用原有的碰撞处理逻辑
        this.onCollision(position, normal);
    }

    /**
     * 爆炸效果，由状态机调用
     */
    public explode(position?: Vec3, normal?: Vec3): void {
        // 使用提供的位置或当前位置
        const explosionPos = position || this._view.node.getWorldPosition();

        // 播放爆炸效果
        this._effectManager.playBulletExplosion(explosionPos);

        // 输出调试信息
        if (normal) {
            // console.log(`碰撞点: (${explosionPos.x.toFixed(2)}, ${explosionPos.y.toFixed(2)}, ${explosionPos.z.toFixed(2)})`);
            // console.log(`碰撞法线: (${normal.x.toFixed(2)}, ${normal.y.toFixed(2)}, ${normal.z.toFixed(2)})`);
        }

        // 延迟一帧转换到销毁状态，确保爆炸效果能够显示
        setTimeout(() => {
            // console.log(`BulletCore: 爆炸效果播放完毕，切换到销毁状态`);
            this.changeState(ProjectileState.DESTROYED);
        }, 100); // 100毫秒延迟，确保爆炸效果可见
    }

    /**
     * 重写碰撞处理方法
     */
    public onCollision(position?: Vec3, normal?: Vec3): void {
        if (this.instanceTriggerExplode) {
            // 先设置爆炸状态信息，再切换状态
            try {
                // 尝试获取爆炸状态
                const states = (this._stateMachine as any).states;
                if (states instanceof Map && states.has(ProjectileState.EXPLODING)) {
                    const explodingState = states.get(ProjectileState.EXPLODING) as BulletExplodingState;
                    if (explodingState && typeof explodingState.setExplosionInfo === 'function') {
                        explodingState.setExplosionInfo(position, normal);
                        console.log(`BulletCore: 设置爆炸状态信息成功`);
                    }
                }
            } catch (e) {
                console.error("设置爆炸状态信息失败:", e);
            }

            // 切换到爆炸状态
            console.log(`BulletCore: 尝试切换到爆炸状态，当前状态: ${this._stateMachine.stateName}`);
            const success = this.changeState(ProjectileState.EXPLODING);
            console.log(`BulletCore: 切换到爆炸状态${success ? '成功' : '失败'}`);
        } else {
            // 如果不触发爆炸，直接销毁
            this.changeState(ProjectileState.DESTROYED);
        }
    }

    /**
     * 根据速度向量更新炮弹旋转
     * @param velocity 速度向量
     */
    protected updateRotationFromVelocity(velocity: Vec3) {
        // 如果速度太小，不更新旋转
        /* if (Vec3.lengthSqr(velocity) < 0.0001) {
            return;
        } */

        // 归一化速度向量
        Vec3.normalize(velocity, velocity);

        // 创建一个四元数，使炮弹的前方（z轴）对齐速度方向
        // 默认前方为 -z 方向
        Quat.fromViewUp(this.rotation, velocity, Vec3.UP);

        // 设置炮弹旋转
    }
}
