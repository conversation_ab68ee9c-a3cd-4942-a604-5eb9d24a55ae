import { _decorator, Component } from 'cc';

const { ccclass } = _decorator;

/**
 * 状态接口
 * 所有状态都应该实现这个接口
 */
export interface IState {
    /**
     * 状态名称
     */
    name: string;

    /**
     * 进入状态时调用
     * @param prevState 前一个状态
     */
    onEnter(prevState?: IState): void;

    /**
     * 状态更新时调用
     * @param dt 时间增量
     */
    onUpdate(dt: number): void;

    /**
     * 离开状态时调用
     * @param nextState 下一个状态
     */
    onExit(nextState?: IState): void;

    /**
     * 状态是否可以过渡到目标状态
     * @param targetState 目标状态
     * @returns 是否可以过渡
     */
    canTransitionTo(targetState: IState): boolean;
}

/**
 * 状态机类
 * 管理状态之间的转换
 */
@ccclass('StateMachine')
export class StateMachine extends Component {
    /**
     * 所有状态的映射
     */
    private states: Map<string, IState> = new Map();

    /**
     * 当前状态
     */
    private currentState: IState = null;

    /**
     * 默认状态
     */
    private defaultState: IState = null;

    /**
     * 状态转换表（可选，如果设置了则优先使用）
     */
    private stateTransitions: Map<string, string[]> = null;

    /**
     * 获取当前状态
     */
    public get state(): IState {
        return this.currentState;
    }

    /**
     * 获取当前状态名称
     */
    public get stateName(): string {
        return this.currentState ? this.currentState.name : 'none';
    }

    /**
     * 设置状态转换表（类似 Game 状态机的风格）
     * @param transitions 状态转换表
     */
    public setTransitions(transitions: Map<string, string[]>): void {
        this.stateTransitions = transitions;
    }

    /**
     * 检查是否可以从当前状态转换到目标状态
     * @param fromState 当前状态名称
     * @param toState 目标状态名称
     * @returns 是否可以转换
     */
    private canTransitionTo(fromState: string, toState: string): boolean {
        // 如果设置了转换表，优先使用转换表
        if (this.stateTransitions) {
            const allowedTransitions = this.stateTransitions.get(fromState);
            if (allowedTransitions) {
                return allowedTransitions.includes(toState);
            }
            // 如果转换表中没有定义当前状态，则不允许转换
            return false;
        }

        // 如果没有转换表，使用状态对象的 canTransitionTo 方法
        const currentStateObj = this.states.get(fromState);
        const targetStateObj = this.states.get(toState);
        if (currentStateObj && targetStateObj) {
            return currentStateObj.canTransitionTo(targetStateObj);
        }

        return false;
    }

    /**
     * 添加状态
     * @param state 状态
     * @param isDefault 是否为默认状态
     */
    public addState(state: IState, isDefault: boolean = false): void {
        if (!state) {
            console.error('StateMachine: Cannot add null state');
            return;
        }

        // 添加到状态映射
        this.states.set(state.name, state);

        // 如果是默认状态，设置为默认状态
        if (isDefault) {
            this.defaultState = state;
        }

        // 如果当前没有状态且这是第一个添加的状态，设置为当前状态
        if (!this.currentState && (isDefault || this.states.size === 1)) {
            this.changeState(state.name);
        }
    }

    /**
     * 移除状态
     * @param stateName 状态名称
     */
    public removeState(stateName: string): void {
        // 如果要移除的是当前状态，先切换到默认状态
        if (this.currentState && this.currentState.name === stateName) {
            if (this.defaultState && this.defaultState.name !== stateName) {
                this.changeState(this.defaultState.name);
            } else {
                this.currentState = null;
            }
        }

        // 如果要移除的是默认状态，清除默认状态
        if (this.defaultState && this.defaultState.name === stateName) {
            this.defaultState = null;
        }

        // 从映射中移除
        this.states.delete(stateName);
    }

    /**
     * 切换状态
     * @param stateName 目标状态名称
     * @returns 是否成功切换
     */
    public changeState(stateName: string): boolean {
        // 获取目标状态
        const targetState = this.states.get(stateName);
        if (!targetState) {
            console.warn(`StateMachine: State '${stateName}' not found`);
            return false;
        }

        // 如果当前状态与目标状态相同，不做任何操作
        if (this.currentState && this.currentState.name === stateName) {
            return true;
        }

        // 检查当前状态是否可以过渡到目标状态
        if (this.currentState && !this.canTransitionTo(this.currentState.name, stateName)) {
            console.warn(`StateMachine: Cannot transition from '${this.currentState.name}' to '${stateName}'`);
            return false;
        }

        // 退出当前状态
        const prevState = this.currentState;
        if (prevState) {
            prevState.onExit(targetState);
        }

        // 设置新状态
        this.currentState = targetState;

        // 进入新状态
        targetState.onEnter(prevState);

        return true;
    }

    /**
     * 更新当前状态
     * @param dt 时间增量
     */
    public update(dt: number): void {
        if (this.currentState) {
            this.currentState.onUpdate(dt);
        }
    }

    /**
     * 重置状态机到默认状态
     */
    public reset(): void {
        if (this.defaultState) {
            this.changeState(this.defaultState.name);
        } else if (this.states.size > 0) {
            // 如果没有默认状态，使用第一个状态
            this.changeState(this.states.keys().next().value);
        } else {
            this.currentState = null;
        }
    }

    /**
     * 强制重置状态机到初始状态，不检查转换规则
     * 用于对象池回收后重新初始化
     */
    public resetToInitialState(): void {
        // 如果当前有状态，强制退出
        if (this.currentState) {
            try {
                this.currentState.onExit(null);
            } catch (e) {
                console.error(`StateMachine: Error exiting state '${this.currentState.name}':`, e);
            }
        }

        // 直接设置为默认状态或第一个状态
        if (this.defaultState) {
            this.currentState = this.defaultState;
            try {
                this.currentState.onEnter(null);
            } catch (e) {
                console.error(`StateMachine: Error entering state '${this.currentState.name}':`, e);
            }
            console.log(`StateMachine: 强制重置到默认状态 '${this.currentState.name}'`);
        } else if (this.states.size > 0) {
            const firstState = this.states.get(this.states.keys().next().value);
            this.currentState = firstState;
            try {
                this.currentState.onEnter(null);
            } catch (e) {
                console.error(`StateMachine: Error entering state '${this.currentState.name}':`, e);
            }
            console.log(`StateMachine: 强制重置到第一个状态 '${this.currentState.name}'`);
        } else {
            this.currentState = null;
            console.log(`StateMachine: 没有可用状态，重置为 null`);
        }
    }

    /**
     * 检查是否处于指定状态
     * @param stateName 状态名称
     * @returns 是否处于指定状态
     */
    public isInState(stateName: string): boolean {
        return this.currentState && this.currentState.name === stateName;
    }
}
