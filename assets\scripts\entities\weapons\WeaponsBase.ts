import { Prefab, Node, Vec3 } from 'cc';
import { WeaponType, StatusEffect } from './WeaponEnums';
import { Character } from '../Character';
import { Vector3Util } from '../../utils/Vector3';
import { WeaponsViewBase } from '../../components/towers/WeaponsViewBase';
import { ProjectileCalculator } from '../../core/ProjectileCalculator';
import { StateMachine } from '../../core/StateMachine';
import { WeaponIdleState, WeaponAttackingState, WeaponDeadState } from './WeaponStates';

// 定义角色状态枚举
export enum WeaponState {
    IDLE = 'idle',
    ATTACKING = 'attacking',
    HURT = 'hurt',
    DEAD = 'dead',

}
export abstract class WeaponsBase {

    protected _weaponPrefab: Prefab;
    protected _weaponParent: Node;
    protected _projectilePrefab: Prefab;
    protected _projectileParent: Node;

    protected _isCharging: boolean = false; // 是否正在蓄力
    protected _chargeTime: number = 0;     // 当前蓄力时间
    protected _maxChargeTime: number = 0.1;  // 最大蓄力时间

    protected _timeScale: number = 1.0;
    protected _moveSpeed: number = 20;
    protected _isAlive: boolean = true;
    protected _weaponType: WeaponType;
    protected _level: number;
    protected _statusEffects: Set<StatusEffect> = new Set<StatusEffect>();    protected _targets: Character[] = [];
    protected _cooldown: number = 0.5;
    protected _currentCooldown: number = 0;
    protected _position: { x: number, y: number, z: number } = { x: 0, y: 0, z: 0 };
    protected _attackRange: number = 100;  // 默认攻击范围

    // 攻击相关属性
    protected _attackDuration: number = 0.2; // 攻击动作持续时间

    // 状态机相关
    protected _stateMachine: StateMachine;

    // 子类需实现 _view 及 createView
    protected _view: WeaponsViewBase | undefined;
    protected abstract createView(prefab: Prefab, parent: Node): WeaponsViewBase;

    constructor(
        weaponType: WeaponType,
        lev: number,
        prefab: Prefab,
        parent: Node,
        prefab2: Prefab,
        parent2: Node,
        position?: { x: number, y: number, z: number },

    ) {
        this._weaponType = weaponType;
        this._level = lev;
        this._weaponPrefab = prefab;
        this._weaponParent = parent;
        this._projectilePrefab = prefab2;
        this._projectileParent = parent2;
        this._position = position || { x: 0, y: 0, z: 0 };
        // 由子类负责 _view 初始化
        // this._view = this.createView(this._weaponPrefab, this._weaponParent);
        this._stateMachine = this.createStateMachine();
    }
    protected updateMainTransform() { }
    // Getters
    get weaponType(): WeaponType { return this._weaponType; }

    get level(): number { return this._level; }

    get statusEffects(): StatusEffect[] { return Array.from(this._statusEffects); }

    get position(): { x: number, y: number, z: number } { return { ...this._position }; }

    get currentStateName(): string {
        return this._stateMachine.stateName;
    }

    get isAlive(): boolean { return this._isAlive; }

    get attackRange(): number { return this._attackRange; }

    set attackRange(value: number) { this._attackRange = value; }

    get firePoint(): Node {
        return this._view?.getFirePoint();
    }
    get firePosition(): Vec3 {
        return this._view?.getFirePosition();
    }
    // 设置位置
    public setPosition(x: number, y: number, z: number): void {
        this._position = { x, y, z };
    }

    // 添加状态效果
    public addStatusEffect(effect: StatusEffect): void {
        this._statusEffects.add(effect);
    }

    // 移除状态效果
    public removeStatusEffect(effect: StatusEffect): void {
        this._statusEffects.delete(effect);
    }    /**
     * 初始化并注册所有武器状态到通用状态机
     */
    protected createStateMachine(): StateMachine {
        const sm = new StateMachine();
        // 添加基本状态 - 子类可以覆盖此方法添加更多自定义状态
        sm.addState(new WeaponIdleState(this), true); // 默认空闲状态
        sm.addState(new WeaponAttackingState(this));  // 攻击状态
        sm.addState(new WeaponDeadState(this));       // 死亡状态
        return sm;
    }
    /**
     * 状态切换统一接口
     */
    public changeState(stateName: string): boolean {
        return this._stateMachine.changeState(stateName);
    }
    /**
     * 状态更新统一接口
     */
    public updateState(dt: number): void {
        this._stateMachine.update(dt);
    }

    /** 获取攻击持续时间 */
    get attackDuration(): number {
        return this._attackDuration;
    }

    /** 检查是否有目标 */
    public hasTargets(): boolean {
        return this._targets && this._targets.length > 0;
    }

    /** 设置目标列表 */
    public setTargets(targets: Character[]): void {
        this._targets = targets || [];
      //  console.log(`武器设置目标: ${this._targets.length}个目标`);
    }

    // 检查是否可以攻击
    public canAttack(): boolean {
        const canAttack = this._currentCooldown <= 0 && this._targets && this._targets.length > 0;
       // console.log(`武器检查是否可以攻击: 冷却=${this._currentCooldown.toFixed(2)}, 目标数=${this._targets?.length || 0}, 结果=${canAttack}`);
        return canAttack;
    }

    // 更新冷却时间
    protected updateCooldown(deltaTime: number): void {

        if (this._currentCooldown > 0) {
            this._currentCooldown -= deltaTime;

        }

    }    // 重置攻击冷却
    public resetCooldown(): void {
        this._currentCooldown = this._cooldown;
    }

    // 基础伤害计算
    protected calculateDamage(baseDamage: number): number {

        return baseDamage;
    }    // 抽象方法实现
    public onDeath(): void {
        // 武器塔被摧毁的处理
        this.changeState(WeaponState.DEAD);
    }


    /**
     * 移动更新，子类可以重写此方法实现移动逻辑
     * @param _dt 时间增量
     */
    protected moveUpdate(_dt: number): void { };
    /**
        * 通过俯仰角和偏航角计算方向向量
        * @param pitch 俯仰角（度数，正值表示向上，负值表示向下）
        * @param yaw 偏航角（度数，0表示向前，正值表示向右）
        * @returns 归一化的方向向量
        */
    protected calculateDirectionFromAngles(pitch: number, yaw: number): { x: number, y: number, z: number } {
        // 将角度转换为弧度
        const pitchRad = pitch * Math.PI / 180;
        const yawRad = yaw * Math.PI / 180;

        // 计算方向向量
        const direction = { x: 0, y: 0, z: 0 };
        direction.x = Math.cos(pitchRad) * Math.sin(yawRad);
        direction.y = Math.sin(pitchRad);
        direction.z = Math.cos(pitchRad) * Math.cos(yawRad);

        // 归一化向量

        return Vector3Util.normalize(direction, direction);
    }
    /**
        * 根据期望的移动速度计算时间缩放
        * @param targetPos 目标位置
        * @param angle 初始角度
        * @param velocity 初始速度
        * @param moveSpeed 期望的移动速度
        * @returns 时间缩放因子
        */
    getMoveTimeScaleByMoveSpeed(targetPos: { x: number, y: number, z: number }, angle: number, velocity: number, moveSpeed: number) {
        const firePos = this.firePoint.getWorldPosition();
        return ProjectileCalculator.getMoveTimeScaleByMoveSpeed(
            { x: firePos.x, y: firePos.y, z: firePos.z },
            targetPos,
            angle,
            velocity,
            moveSpeed
        );
    }

    /**
     * 计算炮弹的预计平均速度（米/秒）
     * @param targetPos 目标位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @param timeScale 时间缩放因子
     * @param useTrajectoryDistance 是否使用轨迹距离而不是直线距离
     * @returns 预计平均速度（米/秒），如果无法到达目标则返回 NaN
     */
    protected calculateExpectedAverageVelocity(targetPos: { x: number, y: number, z: number }, angle: number, velocity: number, timeScale: number = 1.0, useTrajectoryDistance: boolean = true): number {
        const firePos = this.firePoint.getWorldPosition();
        return ProjectileCalculator.calculateExpectedAverageVelocity(
            { x: firePos.x, y: firePos.y, z: firePos.z },
            targetPos,
            angle,
            velocity,
            timeScale,
            useTrajectoryDistance
        );
    }

    /**
        * 计算抛射物到达目标所需的时间
        * @param targetPos 目标位置
        * @param angle 初始角度
        * @param velocity 初始速度
        * @param considerTimeScale 是否考虑时间缩放
        * @returns 到达目标所需的时间
        */
    protected calculateTimeToTarget(targetPos: { x: number, y: number, z: number }, angle: number, velocity: number, considerTimeScale: boolean = false): number {
        const firePos = this.firePoint.getWorldPosition();
        return ProjectileCalculator.calculateTimeToTargetWithPos(
            { x: firePos.x, y: firePos.y, z: firePos.z },
            targetPos,
            angle,
            velocity,
            considerTimeScale,
            this._timeScale
        );
    }    // 更新方法
    public update(deltaTime: number): void {
        // 输出调试信息
        //console.log(`武器更新: 冷却=${this._currentCooldown.toFixed(2)}, 状态=${this._stateMachine.stateName}, 目标数=${this._targets?.length || 0}`);

        // 更新状态机
        this.updateState(deltaTime);

        // 调用子类通用更新
        this.onUpdate(deltaTime);
    }    /**
     * 执行攻击操作
     * 子类可以重写此方法以实现特定武器的攻击逻辑
     */
    public performAttack(): void {
        // 基础武器不执行任何攻击操作
        // 由子类重写实现具体武器的攻击逻辑
        //console.log('执行基础攻击');
    }

    protected onUpdate(deltaTime: number): void {
        // 1. 冷却计时等通用逻辑
        this.updateCooldown(deltaTime);

        // 2. 视图更新
        if (this._view) {
            this._view.updateView(deltaTime);
        }

        // 3. 移动更新
        this.moveUpdate(deltaTime);
    }
}