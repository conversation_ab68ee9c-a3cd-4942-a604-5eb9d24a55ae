import { _decorator } from 'cc';
import { IState } from './StateMachine';
import { Character } from './Character';

const { ccclass } = _decorator;

/**
 * 角色状态基类
 * 所有角色状态都应该继承这个类
 */
@ccclass('CharacterState')
export abstract class CharacterState implements IState {
    /**
     * 状态名称
     */
    public abstract name: string;

    /**
     * 拥有该状态的角色
     */
    protected character: Character;

    /**
     * 状态持续时间
     */
    protected duration: number = 0;

    /**
     * 状态是否可以被打断
     */
    protected canBeInterrupted: boolean = true;

    /**
     * 构造函数
     * @param character 角色
     */
    constructor(character: Character) {
        this.character = character;
    }

    /**
     * 进入状态时调用
     * @param prevState 前一个状态
     */
    public onEnter(prevState?: IState): void {
        // 重置状态持续时间
        this.duration = 0;
    }

    /**
     * 状态更新时调用
     * @param dt 时间增量
     */
    public onUpdate(dt: number): void {
        // 更新状态持续时间
        this.duration += dt;
    }

    /**
     * 离开状态时调用
     * @param nextState 下一个状态
     */
    public onExit(nextState?: IState): void {
        // 基类不做任何操作
    }

    /**
     * 状态是否可以过渡到目标状态
     * @param targetState 目标状态
     * @returns 是否可以过渡
     */
    public canTransitionTo(targetState: IState): boolean {
        // 默认情况下，如果状态可以被打断，则可以过渡到任何状态
        return this.canBeInterrupted;
    }

    /**
     * 获取状态持续时间
     * @returns 状态持续时间
     */
    public getDuration(): number {
        return this.duration;
    }

    /**
     * 设置状态是否可以被打断
     * @param value 是否可以被打断
     */
    public setCanBeInterrupted(value: boolean): void {
        this.canBeInterrupted = value;
    }
}

/**
 * 生成状态
 * 角色生成时的状态
 */
@ccclass('SpawnState')
export class SpawnState extends CharacterState {
    public name: string = 'spawn';

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);
        // 播放生成动画
        this.character.playAnimation('spawn');
        // 设置为不可被打断
        this.canBeInterrupted = false;
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        // 如果动画播放完毕，切换到空闲状态
        if (this.character.isAnimationFinished()) {
            this.character.changeState('idle');
        }
    }
}

/**
 * 空闲状态
 * 角色不做任何操作时的状态
 */
@ccclass('IdleState')
export class IdleState extends CharacterState {
    public name: string = 'idle';

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);
        // 播放空闲动画
        this.character.playAnimation('idle');
    }
}

/**
 * 移动状态
 * 角色移动时的状态
 */
@ccclass('MoveState')
export class MoveState extends CharacterState {
    public name: string = 'move';

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);
        // 播放移动动画
        this.character.playAnimation('move');
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        // 更新移动
        this.character.updateMovement(dt);
    }
}

/**
 * 攻击状态
 * 角色攻击时的状态
 */
@ccclass('AttackState')
export class AttackState extends CharacterState {
    public name: string = 'attack';

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);
        // 播放攻击动画
        this.character.playAnimation('attack');
        // 设置为不可被打断
        this.canBeInterrupted = false;
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        // 如果动画播放完毕，切换到空闲状态
        if (this.character.isAnimationFinished()) {
            this.character.changeState('idle');
        }
    }
}

/**
 * 受击状态
 * 角色被攻击时的状态
 */
@ccclass('HitState')
export class HitState extends CharacterState {
    public name: string = 'hit';

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);
        // 播放受击动画
        this.character.playAnimation('hit');
        // 设置为不可被打断
        this.canBeInterrupted = false;
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        // 如果动画播放完毕，切换到空闲状态
        if (this.character.isAnimationFinished()) {
            this.character.changeState('idle');
        }
    }
}

/**
 * 死亡状态
 * 角色死亡时的状态
 */
@ccclass('DeathState')
export class DeathState extends CharacterState {
    public name: string = 'death';

    public onEnter(prevState?: IState): void {
        super.onEnter(prevState);
        // 播放死亡动画
        this.character.playAnimation('death');
        // 设置为不可被打断
        this.canBeInterrupted = false;
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        // 死亡状态不会自动切换到其他状态
    }

    public canTransitionTo(targetState: IState): boolean {
        // 死亡状态不能过渡到任何状态
        return false;
    }
}
