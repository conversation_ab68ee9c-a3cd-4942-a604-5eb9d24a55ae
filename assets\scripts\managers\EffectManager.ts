import { Vec3, Prefab, instantiate, director } from 'cc';

/**
 * 效果管理器
 */
export class EffectManager {
    private bulletExplosionPrefab: Prefab | null = null;

    // 通过注入Prefab
    public setBulletExplosionPrefab(prefab: Prefab) {
        this.bulletExplosionPrefab = prefab;
    }

    /**
     * 播放炮弹爆炸效果
     * @param pos 
     */
    public playBulletExplosion(pos: Vec3) {
        if (!this.bulletExplosionPrefab) {
            console.warn('EffectManager: bulletExplosionPrefab 未注入');
            return;
        }
        // 生成节点
        const node = instantiate(this.bulletExplosionPrefab);
        director.getScene().addChild(node);
        node.setWorldPosition(pos);
        // 定时销毁
        // 由于不再继承Component，不能用this.scheduleOnce，改用setTimeout
        setTimeout(() => node.destroy(), 1000);
    }
}
