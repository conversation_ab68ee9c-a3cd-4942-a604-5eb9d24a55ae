import { _decorator, Component } from 'cc';

import { EnemyViewBase } from './EnemyViewBase';
const { ccclass, property } = _decorator;

@ccclass('ZombieEnemyView')
export class ZombieEnemyView extends EnemyViewBase {



    // 可选：每帧同步位置
    updateView(dt: number) {
        if (this._logic && this._logic.isAlive) {
            const pos = this._logic.position;
            if (this.node && this.node.isValid) {
                this.node?.setWorldPosition(pos.x, pos.y, pos.z);
            }

        }
    }

    // 可扩展：受击、死亡等表现
    public playHitEffect(): void {
        // 可自定义受击动画
        super.playHitEffect();
    }

    public playDeathEffect(): void {
        // 可自定义死亡动画
        super.playDeathEffect();
    }

    // 可扩展僵尸表现层逻辑，如动画、特效等
}
