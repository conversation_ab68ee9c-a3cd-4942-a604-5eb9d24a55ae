import { _decorator, Component } from 'cc';

import { EnemyViewBase } from './EnemyViewBase';
const { ccclass, property } = _decorator;

@ccclass('ZombieEnemyView')
export class ZombieEnemyView extends EnemyViewBase {



    // 可选：每帧同步位置
    updateView(dt: number) {
        if (this._logic && this._logic.isAlive) {
            const pos = this._logic.position;
            if (this.node && this.node.isValid) {
                // 获取当前世界位置
                const currentWorldPos = this.node.getWorldPosition();

                // 检查是否需要更新位置
                const positionDiff = Math.sqrt(
                    Math.pow(currentWorldPos.x - pos.x, 2) +
                    Math.pow(currentWorldPos.y - pos.y, 2) +
                    Math.pow(currentWorldPos.z - pos.z, 2)
                );

                // 只在位置差异较大时更新，避免频繁设置
                if (positionDiff > 0.01) {
                    this.node.setWorldPosition(pos.x, pos.y, pos.z);
                    console.log(`ZombieEnemyView: 位置同步 - 逻辑位置: (${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)}), 差异: ${positionDiff.toFixed(3)}`);
                }
            }
        }
    }

    // 可扩展：受击、死亡等表现
    public playHitEffect(): void {
        // 可自定义受击动画
        super.playHitEffect();
    }

    public playDeathEffect(): void {
        // 可自定义死亡动画
        super.playDeathEffect();
    }

    // 可扩展僵尸表现层逻辑，如动画、特效等
}
