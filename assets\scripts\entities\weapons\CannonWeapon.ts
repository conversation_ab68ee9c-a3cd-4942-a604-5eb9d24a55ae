import { Prefab, Node, instantiate, Vec3 } from "cc";
import { WeaponsBase } from "./WeaponsBase";
import { WeaponType } from "./WeaponEnums";
import { CannonWeaponView } from "../../components/towers/CannonWeaponView";
import { ProjectileCalculator } from "../../core/ProjectileCalculator";
import { Character } from "../Character";
import { BulletCore } from "../projectiles/BulletCore";
import { Inject } from "../../decorators/Inject";
import { IGameManager } from "../../interfaces/IManager";
import { CollisionDetectionMode, ProjectileMode } from "../projectiles/ProjectileCore";
import { StateMachine } from '../../core/StateMachine';
import { WeaponIdleState, WeaponDeadState } from './WeaponStates';
import { CannonAttackingState } from './CannonWeaponStates';


/**
 * 发射器模式
 */
export enum LauncherMode {
    /** 固定角度和速度 */
    FIXED_ALL = 1,
    /** 固定俯仰角 */
    FIXED_PITCH_ANGLE = 2,
    /** 固定速度 */
    FIXED_VELOCITY = 3,
    /** 不固定角度和速度 */
    UNFIXED = 4,
}
export class CannonWeapon extends WeaponsBase {
    @Inject('gameManager')
    protected _gameManager: IGameManager;
    //武器模式
    public mode: LauncherMode = LauncherMode.UNFIXED;
    //炮弹模式
    public projectileMode: ProjectileMode = ProjectileMode.MATH;
    //固定俯仰角
    public fixedPitchAngle: number = -15;
    //固定初速度
    public fixedVelocity: number = 5;

    /**
        * 速度
        */
    protected velocity: number = 0;

    /**
     * 方向向量
     */
    protected directionVec: Vec3 = new Vec3();

    /**
     * 当前目标位置
     */
    protected curTargetPos: Vec3 = new Vec3();

    /**
     * 主体位置
     */
    protected mainPosition: Vec3 = new Vec3();

    /**
     * 主体前方向
     */
    protected mainForward: Vec3 = new Vec3();

    /**
     * 主体上方向
     */
    protected mainUp: Vec3 = new Vec3();

    /**
       * 俯仰角
       */
    public get pitch() {
        return this._view.getPitch()
    }
    protected set pitch(value: number) {
        this._view.setPitch(value);
    }

    /**
     * 偏航角
     */
    public get yaw() {
        return this._view.getYaw()
    }
    protected set yaw(value: number) {
        this._view.setYaw(value);
    }


    constructor(prefab: Prefab, parent: Node, projectilePrefab: Prefab, projectileParent: Node, position?: { x: number, y: number, z: number }) {
        super(WeaponType.BOW, 1, prefab, parent, projectilePrefab, projectileParent, position);
        // 不能直接 this._view = ...，需用 Object.defineProperty 或在 super 之后初始化
        Object.defineProperty(this, '_view', {
            value: this.createView(this._weaponPrefab, this._weaponParent),
            writable: true,
            configurable: true
        });
        this.updateMainTransform();
        // 初始化通用状态机
        this._stateMachine = this.createStateMachine();
    }    /**
     * 创建并注册所有武器状态到通用状态机
     */
    protected override createStateMachine(): StateMachine {
        const sm = new StateMachine();
        sm.addState(new WeaponIdleState(this), true);     // 默认空闲状态
        sm.addState(new CannonAttackingState(this));      // 自定义攻击状态
        sm.addState(new WeaponDeadState(this));           // 死亡状态
        return sm;
    }

    /**
        * 更新主体变换
        * 重写父类的方法，以适应大炮的特殊结构
        */
    protected updateMainTransform() {
        let yaw = this._view.getYawAxis();
        // 获取主体位置
        this.mainPosition.set(yaw.getWorldPosition());
        // 获取主体前方向
        this.mainForward.set(yaw.parent.forward).negative();
        // 获取主体上方向
        this.mainUp.set(yaw.up);
    }

    protected createView(createView: Prefab, parent: Node): CannonWeaponView {
        const node = instantiate(createView);
        parent.addChild(node);

        const view = node.getComponent(CannonWeaponView);
        if (!view) {
            throw new Error("Failed to get CannonWeaponView component from prefab.");
        }
        view.setLogic(this);
        return view;
    }
    // 设置目标
    public setTargets(targets: Character[]): void {
        this._targets = targets.filter(target =>
            target.isAlive

        );
    }
    getTarget(): Character | undefined {
        let target = this._targets
            .filter(target => target.isAlive)
            .sort((a, b) => {
                const distA = Math.hypot(a.position.x - this._position.x, a.position.y - this._position.y);
                const distB = Math.hypot(b.position.x - this._position.x, b.position.y - this._position.y);
                return distA - distB;
            })[0];
        return target
    }
    getTargets(): Character[] {
        let targets = this._targets
            .filter(target => target.isAlive)
            .sort((a, b) => {
                const distA = Math.hypot(a.position.x - this._position.x, a.position.y - this._position.y);
                const distB = Math.hypot(b.position.x - this._position.x, b.position.y - this._position.y);
                return distA - distB;
            });
        return targets
    }

    /**
     * 计算瞄准参数，但不改变大炮状态
     * @param targetPos 目标位置
     * @returns 计算的瞄准参数（偏航角、俯仰角和速度）
     */
    protected calculateAimParameters(targetPos: Vec3): { yawAngle: number, pitchAngle: number, velocity: number } {
        // 更新主体变换（不改变大炮状态）
        const mainPosition = new Vec3();
        this.firePoint.getWorldPosition(mainPosition);

        // 计算方向向量
        const directionVec = new Vec3();
        Vec3.subtract(directionVec, targetPos, mainPosition);

        // 计算偏航角
        let yawAngle = this.calculateYawAngleWithoutState(directionVec);

        // 计算俯仰角和速度
        const { pitchAngle, velocity } = this.calculatePitchAndVelocity(targetPos);

        return { yawAngle, pitchAngle, velocity };
    }

    /**
     * 计算偏航角但不改变状态
     * @param directionVec 方向向量
     * @returns 偏航角
     */
    protected calculateYawAngleWithoutState(directionVec: Vec3): number {
        // 特殊情况处理：当目标位置与主体位置在同一垂直线上时
        if (Math.abs(directionVec.x) < 0.001 && Math.abs(directionVec.z) < 0.001) {
            return this.yaw; // 保持当前偏航角
        }

        // 将方向向量投影到水平面上（忽略y分量）
        const dirHorizontal = new Vec3(directionVec.x, 0, directionVec.z);

        // 计算水平方向上的角度（使用 atan2）
        const targetAngle = Math.atan2(dirHorizontal.z, dirHorizontal.x) * 180 / Math.PI;

        // 计算偏航角
        let yawAngle = 90 - targetAngle;

        // 规范化角度到 -180 到 180 度范围
        while (yawAngle > 180) yawAngle -= 360;
        while (yawAngle < -180) yawAngle += 360;

        return yawAngle;
    }

    // 存储计算出的时间缩放因子，供attack方法使用
    protected _calculatedTimeScale: number = 1.0;

    /**
     * 检查目标是否为远距离目标
     * @param targetPos 目标位置
     * @returns 是否为远距离目标
     */
    protected isLongDistanceTarget(targetPos: Vec3): boolean {
        const firePos = new Vec3();
        this.firePoint.getWorldPosition(firePos);
        const distance = Vec3.distance(targetPos, firePos);

        // 距离大于30单位视为远距离目标
        return distance > 30;
    }

    /**
     * 重写获取目标位置方法，支持预测
     * @param predict 是否预测目标位置
     * @returns 目标位置
     */
    getTargetPos(predict: boolean = false): { x: number, y: number, z: number } {
        // 获取目标
        const target = this.getTarget();
        if (!target) {
            return null;
        }

        // 如果不需要预测，直接返回当前位置
        if (!predict) {
            return target.position;
        }

        // 获取目标当前位置并转换为Vec3
        const currentTargetPos = new Vec3(
            target.position.x,
            target.position.y,
            target.position.z
        );

        // 检查是否为远距离目标
        const isLongDistance = this.isLongDistanceTarget(currentTargetPos);

        // 计算初始瞄准参数（不改变大炮状态）
        const initialParams = this.calculateAimParameters(currentTargetPos);

        // 计算子弹到达当前目标位置所需的时间（游戏内时间）
        const initialGameTimeToTarget = this.calculateTimeToTargetWithParams(
            currentTargetPos,
            -initialParams.pitchAngle,
            initialParams.velocity
        );

        // 计算时间缩放因子
        let timeScale = this.getMoveTimeScaleByMoveSpeed(
            currentTargetPos,
            -initialParams.pitchAngle,
            initialParams.velocity,
            this._moveSpeed
        );

        // 对于远距离目标，调整时间缩放因子
        if (isLongDistance) {
            // 远距离目标可能需要更大的时间缩放因子
            timeScale *= 1.1;
        }

        // 保存计算出的时间缩放因子，供attack方法使用
        this._calculatedTimeScale = timeScale;

        // 计算实际时间（考虑时间缩放）
        const initialRealTimeToTarget = initialGameTimeToTarget / timeScale;

        // 使用实际时间预测目标未来位置
        const predictedTargetPos = this.predictTargetPosition(target, initialRealTimeToTarget);

        // 计算预测位置的瞄准参数
        const refinedParams = this.calculateAimParameters(predictedTargetPos);

        // 再次计算时间，获得更精确的预测（游戏内时间）
        const refinedGameTimeToTarget = this.calculateTimeToTargetWithParams(
            predictedTargetPos,
            -refinedParams.pitchAngle,
            refinedParams.velocity
        );

        // 计算实际时间（考虑时间缩放）
        const refinedRealTimeToTarget = refinedGameTimeToTarget / timeScale;

        // 使用精确实际时间再次预测目标位置
        const finalPredictedPos = this.predictTargetPosition(target, refinedRealTimeToTarget);

        // 对于远距离目标，可能需要额外的迭代来提高精度
        if (isLongDistance) {
            // 第三次迭代，进一步提高远距离预测精度
            const thirdParams = this.calculateAimParameters(finalPredictedPos);
            const thirdGameTimeToTarget = this.calculateTimeToTargetWithParams(
                finalPredictedPos,
                -thirdParams.pitchAngle,
                thirdParams.velocity
            );
            const thirdRealTimeToTarget = thirdGameTimeToTarget / timeScale;

            // 最终预测位置
            const ultimatePredictedPos = this.predictTargetPosition(target, thirdRealTimeToTarget);

            console.log("远距离目标预测(三次迭代):", {
                距离: Vec3.distance(currentTargetPos, this.firePoint.getWorldPosition()).toFixed(2),
                游戏内时间: thirdGameTimeToTarget.toFixed(2) + "秒",
                时间缩放: timeScale.toFixed(2),
                实际时间: thirdRealTimeToTarget.toFixed(2) + "秒",
                预测位置: {
                    x: ultimatePredictedPos.x.toFixed(2),
                    y: ultimatePredictedPos.y.toFixed(2),
                    z: ultimatePredictedPos.z.toFixed(2)
                },
                预测偏移: {
                    x: (ultimatePredictedPos.x - currentTargetPos.x).toFixed(2),
                    y: (ultimatePredictedPos.y - currentTargetPos.y).toFixed(2),
                    z: (ultimatePredictedPos.z - currentTargetPos.z).toFixed(2),
                    距离: Vec3.distance(ultimatePredictedPos, currentTargetPos).toFixed(2)
                }
            });

            return {
                x: ultimatePredictedPos.x,
                y: ultimatePredictedPos.y,
                z: ultimatePredictedPos.z
            };
        }

        console.log("目标预测考虑时间缩放:", {
            距离: Vec3.distance(currentTargetPos, this.firePoint.getWorldPosition()).toFixed(2),
            游戏内时间: initialGameTimeToTarget.toFixed(2) + "秒",
            时间缩放: timeScale.toFixed(2),
            实际时间: initialRealTimeToTarget.toFixed(2) + "秒",
            预测位置: {
                x: finalPredictedPos.x.toFixed(2),
                y: finalPredictedPos.y.toFixed(2),
                z: finalPredictedPos.z.toFixed(2)
            },
            预测偏移: {
                x: (finalPredictedPos.x - currentTargetPos.x).toFixed(2),
                y: (finalPredictedPos.y - currentTargetPos.y).toFixed(2),
                z: (finalPredictedPos.z - currentTargetPos.z).toFixed(2),
                距离: Vec3.distance(finalPredictedPos, currentTargetPos).toFixed(2)
            }
        });

        return {
            x: finalPredictedPos.x,
            y: finalPredictedPos.y,
            z: finalPredictedPos.z
        };
    }
    /*  updateAim() {
         const mainTargetPos = this.getTargetPos();
         if (!mainTargetPos) return;

         //瞄准目标 计算获得： 1、计算偏航角yaw 2、计算俯仰角pitch 3、速度velocity
         this.aim(mainTargetPos);
     } */
    /**
     * 获取目标的实际速度向量
     * 这个方法解决了Character.getVelocityVector可能返回零向量的问题
     * @param target 目标角色
     * @returns 速度向量
     */
    protected getActualVelocityVector(target: Character): { x: number, y: number, z: number } {
        // 首先尝试使用目标自己的getVelocityVector方法
        const velocityVector = target.getVelocityVector();

        // 计算速度大小
        Math.sqrt(
            velocityVector.x * velocityVector.x +
            velocityVector.y * velocityVector.y +
            velocityVector.z * velocityVector.z
        );


        return velocityVector;
    }

    /**
     * 预测目标未来位置
     * @param target 目标角色
     * @param timeToTarget 预计到达时间
     * @returns 预测的未来位置
     */
    protected predictTargetPosition(target: Character, timeToTarget: number): Vec3 {
        // 获取目标当前位置
        const currentPos = target.position;

        // 获取目标实际速度向量（改进版）
        const velocityVector = this.getActualVelocityVector(target);

        // 应用速度缩放因子，提高预测精度
        // 根据实际测试调整这个值，范围通常在0.8-1.2之间
        // 如果预测位置总是落后于目标，增大这个值
        // 如果预测位置总是超前于目标，减小这个值
        const velocityScaleFactor = 1.1;

        // 预测未来位置 = 当前位置 + 速度 * 时间 * 缩放因子
        const predictedPos = new Vec3(
            currentPos.x + velocityVector.x * timeToTarget * velocityScaleFactor,
            currentPos.y + velocityVector.y * timeToTarget * velocityScaleFactor,
            currentPos.z + velocityVector.z * timeToTarget * velocityScaleFactor
        );

        // 添加一个小的预测修正，基于目标的移动方向
        // 这有助于处理目标可能的加速或转向
        const speed = Math.sqrt(
            velocityVector.x * velocityVector.x +
            velocityVector.y * velocityVector.y +
            velocityVector.z * velocityVector.z
        );

        // 如果目标速度足够大，添加额外的预测修正
        if (speed > 0.5) {
            // 计算移动方向
            const moveDir = new Vec3(
                velocityVector.x / speed,
                velocityVector.y / speed,
                velocityVector.z / speed
            );

            // 添加基于移动方向的额外预测，随着时间增加而增加
            // 这有助于处理目标可能的加速
            const extraPredictionFactor = 0.5 * timeToTarget;
            predictedPos.x += moveDir.x * extraPredictionFactor;
            predictedPos.y += moveDir.y * extraPredictionFactor;
            predictedPos.z += moveDir.z * extraPredictionFactor;
        }

        console.log("目标预测:", {
            currentPos: currentPos,
            velocityVector: velocityVector,
            speed: speed,
            timeToTarget: timeToTarget,
            velocityScaleFactor: velocityScaleFactor,
            predictedPos: predictedPos,
            预测偏移: {
                x: predictedPos.x - currentPos.x,
                y: predictedPos.y - currentPos.y,
                z: predictedPos.z - currentPos.z
            }
        });

        return predictedPos;
    }

    /**
     * 计算到达目标所需的时间
     * @param targetPos 目标位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @returns 到达目标所需的时间
     */
    protected calculateTimeToTarget(targetPos: Vec3, angle: number, velocity: number): number {
        const { horizontal, vertical } = ProjectileCalculator.calculateDisplacement(this.firePoint.getWorldPosition(), targetPos);
        return ProjectileCalculator.calculateTimeToTargetPrecise(horizontal, vertical, angle, velocity);
    }

    /**
     * 使用指定参数计算到达目标所需的时间
     * @param targetPos 目标位置
     * @param angle 初始角度
     * @param velocity 初始速度
     * @returns 到达目标所需的时间
     */
    protected calculateTimeToTargetWithParams(targetPos: Vec3, angle: number, velocity: number): number {
        const firePos = new Vec3();
        this.firePoint.getWorldPosition(firePos);
        const { horizontal, vertical } = ProjectileCalculator.calculateDisplacement(firePos, targetPos);
        return ProjectileCalculator.calculateTimeToTargetPrecise(horizontal, vertical, angle, velocity);
    }    public attack(): void {
        console.log("CannonWeapon attack");
        //获取目标角色
        if (!this.canAttack()) return;

        // 获取目标角色
        const target = this.getTarget();
        if (!target) return;

        // 获取目标当前位置
        const currentTargetPos = new Vec3(
            target.position.x,
            target.position.y,
            target.position.z
        );

        // 获取预测位置
        const predictedPos = this.getTargetPos(true);
        if (!predictedPos) return;

        // 将预测位置转换为Vec3
        const finalPredictedPos = new Vec3(
            predictedPos.x,
            predictedPos.y,
            predictedPos.z
        );

        // 只在最终确定目标位置后，调用一次aim方法
        this.aim(finalPredictedPos);

        // 使用预先计算好的时间缩放因子，而不是重新计算
        // 这确保预测位置和实际发射使用相同的时间缩放
        this._timeScale = this._calculatedTimeScale;

        // 计算方向向量（通过俯仰角和偏航角计算）
        const direction = this.calculateDirectionFromAngles(-this.pitch, this.yaw);

        // 使用 GameManager 的 createProjectile 方法创建子弹
        // 优先从对象池获取，如果对象池中没有可用对象，则创建新的子弹
        const bullet = this._gameManager.createProjectile('bullet', () => {
            // 这个工厂函数只有在对象池中没有可用对象时才会被调用
            return new BulletCore(this._projectilePrefab, this._projectileParent);
        });

        // 检查是否为远距离目标
        const isLongDistance = this.isLongDistanceTarget(currentTargetPos);

        // 对于远距离目标，可能需要调整碰撞检测模式或其他参数
        const collisionMode = isLongDistance ? 1 : 1; // 可以根据需要调整
        // 物理模式
        this.directionVec.set(direction.x, direction.y, direction.z).multiplyScalar(this.velocity);
        // 初始化弹道信息
        if (this.projectileMode == ProjectileMode.MATH) {
            bullet.initMathMode(-this.pitch, this.velocity, direction, this.firePosition, this.curTargetPos, collisionMode, undefined, undefined, this._timeScale);
        }
        else {
            bullet.initPhysicsMode(this.directionVec, this.firePosition, this.firePoint.getWorldRotation(), CollisionDetectionMode.NONE, undefined, undefined, this._timeScale);
        }


        // 记录远距离目标的特殊处理
        if (isLongDistance) {
            console.log("远距离目标特殊处理:", {
                距离: Vec3.distance(currentTargetPos, this.firePosition).toFixed(2),
                时间缩放: this._timeScale.toFixed(2),
                碰撞模式: collisionMode
            });
        }

        console.log("目标预测攻击:", {
            currentPos: {
                x: currentTargetPos.x,
                y: currentTargetPos.y,
                z: currentTargetPos.z
            },
            predictedPos: {
                x: finalPredictedPos.x,
                y: finalPredictedPos.y,
                z: finalPredictedPos.z
            },
            预测偏移: {
                x: finalPredictedPos.x - currentTargetPos.x,
                y: finalPredictedPos.y - currentTargetPos.y,
                z: finalPredictedPos.z - currentTargetPos.z
            },
            yaw: this.yaw,
            pitch: this.pitch,
            velocity: this.velocity,
            timeScale: this._timeScale
        });
    }

    /**
      * 瞄准
      * @param targetPos 目标位置
      */
    public aim(targetPos: { x: number, y: number, z: number }) {
        // 保存目标位置
        this.curTargetPos.set(targetPos.x, targetPos.y, targetPos.z);
        this.updateMainTransform();
        // 计算偏航角
        const yawAngle = this.calculateYawAngle(this.curTargetPos);
        this.yaw = yawAngle;

        // 计算俯仰角和速度
        const { pitchAngle, velocity } = this.calculatePitchAndVelocity(this.curTargetPos);

        // 设置俯仰角和速度
        if (!isNaN(pitchAngle)) {
            this.pitch = pitchAngle;
        }
        if (!isNaN(velocity)) {
            this.velocity = velocity;
        }
        // console.log("瞄准：", targetPos, this.yaw, this.pitch, this.velocity)
    }



    /**
    * 计算偏航角
    * @param targetPos 目标位置
    * @returns 偏航角
    */
    protected calculateYawAngle(targetPos: Vec3): number {
        // 大炮直接看向目标点的方向矢量
        Vec3.subtract(this.directionVec, targetPos, this.mainPosition);

        // 特殊情况处理：当目标位置与主体位置在同一垂直线上时
        if (Math.abs(this.directionVec.x) < 0.001 && Math.abs(this.directionVec.z) < 0.001) {
            // 目标在正上方或正下方，保持当前偏航角
            console.log("目标在正上方或正下方，保持当前偏航角");
            return this.yaw;
        }

        // 将方向向量投影到水平面上（忽略y分量）
        const dirHorizontal = new Vec3(this.directionVec.x, 0, this.directionVec.z);

        // 计算水平方向上的角度（使用 atan2）
        // atan2(z, x) 给出的是相对于 x 轴正方向的角度
        const targetAngle = Math.atan2(dirHorizontal.z, dirHorizontal.x) * 180 / Math.PI;

        // 计算偏航角（相对于世界坐标系）
        // 在 Cocos Creator 中，偏航角是绕 y 轴旋转的角度
        // 我们需要将目标角度转换为偏航角
        let yawAngle = 90 - targetAngle; // 将 atan2 的结果转换为偏航角

        // 规范化角度到 -180 到 180 度范围
        while (yawAngle > 180) yawAngle -= 360;
        while (yawAngle < -180) yawAngle += 360;

        console.log("计算偏航角:", {
            targetPos: targetPos.clone(),
            mainPosition: this.mainPosition.clone(),
            directionVec: this.directionVec.clone(),
            dirHorizontal: dirHorizontal.clone(),
            targetAngle,
            yawAngle
        });

        return yawAngle;
    }


    /**
     * 计算俯仰角和速度
     * @param targetPos 目标位置
     * @returns 俯仰角、速度和是否固定弹道距离
     */
    protected calculatePitchAndVelocity(targetPos: Vec3): { pitchAngle: number, velocity: number, fixedTrajectoryDistance: boolean } {
        let pitchAngle = NaN;
        let velocity = NaN;
        let fixedTrajectoryDistance = false;
        console.log('mode:', this.mode)
        switch (this.mode) {
            // 固定角度和速度
            case LauncherMode.FIXED_ALL: {
                pitchAngle = this.fixedPitchAngle;
                velocity = this.fixedVelocity;
                // 固定弹道计算的水平位移
                fixedTrajectoryDistance = true;
                break;
            }
            // 固定俯仰角
            case LauncherMode.FIXED_PITCH_ANGLE: {
                pitchAngle = this.fixedPitchAngle;
                // 计算速度时以斜向上的角度为正
                velocity = this.calculateVelocity(targetPos, -pitchAngle);
                break;
            }
            // 固定速度
            case LauncherMode.FIXED_VELOCITY: {
                velocity = this.fixedVelocity;
                // 计算角度
                const selectedAngle = this.calculatePitchAngle(targetPos, velocity);

                // 如果角度有效
                if (!isNaN(selectedAngle)) {
                    // 设置角度
                    pitchAngle = selectedAngle;
                } else {
                    // 固定弹道计算的水平位移
                    fixedTrajectoryDistance = true;
                }
                break;
            }
            // 不固定角度和速度
            case LauncherMode.UNFIXED: {
                const result = this.calculateWithMaxHeight(targetPos);
                pitchAngle = -result.angle;
                velocity = result.velocity;
                console.log('calculateWithMaxHeight', pitchAngle)
                break;
            }
        }

        // 角度值是否有效
        if (isNaN(pitchAngle)) {
            pitchAngle = this.pitch;    // 维持当前角度
        }

        return { pitchAngle, velocity, fixedTrajectoryDistance };
    }
    /**
      * 计算速度
      * @param targetPos 目标位置
      * @param angle 角度
      * @returns 速度
      */
    protected calculateVelocity(targetPos: Vec3, angle: number): number {
        const { horizontal, vertical } = ProjectileCalculator.calculateDisplacement(this.firePoint.getWorldPosition(), targetPos);
        return ProjectileCalculator.calculateWithAngle(horizontal, vertical, angle);
    }

    /**
     * 计算俯仰角
     * @param targetPos 目标位置
     * @param velocity 速度
     * @returns 俯仰角
     */
    protected calculatePitchAngle(targetPos: Vec3, velocity: number): number {
        const { horizontal, vertical } = ProjectileCalculator.calculateDisplacement(this.firePoint.getWorldPosition(), targetPos);
        const angles = ProjectileCalculator.calculateWithVelocity(horizontal, vertical, velocity);

        // 选择合适的角度
        let selectedAngle: number;
        if (!isNaN(angles.angle1) && !isNaN(angles.angle2)) {
            // 两个角度都有效，选择较小的那个（通常是低弹道）
            selectedAngle = Math.abs(angles.angle1) < Math.abs(angles.angle2) ? angles.angle1 : angles.angle2;
        } else if (!isNaN(angles.angle1)) {
            selectedAngle = angles.angle1;
        } else if (!isNaN(angles.angle2)) {
            selectedAngle = angles.angle2;
        } else {
            // 无法到达目标
            selectedAngle = NaN;
        }

        return selectedAngle;
    }
    /**
   * 根据最大高度计算速度和角度
   * @param targetPos 目标位置
   */
    protected calculateWithMaxHeight(targetPos: Vec3) {
        // 计算水平和垂直位移
        const { horizontal, vertical } = ProjectileCalculator.calculateDisplacement(this.firePoint.getWorldPosition(), targetPos);

        // 根据目标高度调整角度
        let angle = 45; // 默认角度

        // 如果目标位置较高，增加角度
        if (vertical > 0) {
            // 计算目标高度与水平距离的比例
            const heightRatio = vertical / horizontal;

            // 根据比例调整角度
            if (heightRatio > 0.5) {
                // 目标位置很高，使用更大的角度
                angle = Math.min(75, 45 + heightRatio * 30); // 最大限制在75度
                console.log(`目标位置较高 (高度比例: ${heightRatio.toFixed(2)}), 调整角度为 ${angle.toFixed(2)}°`);
            }
        }

        // 计算速度
        let velocity = this.calculateVelocity(targetPos, angle);

        // 如果计算出的速度无效或太大，尝试不同的角度
        if (isNaN(velocity) || velocity > 100) {
            console.log(`使用角度 ${angle.toFixed(2)}° 计算的速度无效或太大: ${velocity}, 尝试其他角度`);

            // 尝试一系列角度
            const testAngles = [30, 60, 75];
            for (const testAngle of testAngles) {
                const testVelocity = this.calculateVelocity(targetPos, testAngle);
                if (!isNaN(testVelocity) && testVelocity < 100) {
                    angle = testAngle;
                    velocity = testVelocity;
                    console.log(`找到有效角度: ${angle.toFixed(2)}°, 速度: ${velocity.toFixed(2)}`);
                    break;
                }
            }
        }

        // 如果仍然无法计算有效速度，使用一个合理的默认值
        if (isNaN(velocity) || velocity > 100) {
            // 估算一个合理的速度
            const distance = Vec3.distance(targetPos, this.firePoint.getWorldPosition());
            velocity = Math.max(10, Math.min(50, distance * 0.5));
            console.log(`无法计算有效速度，使用估计值: ${velocity.toFixed(2)}`);
        }

        return { angle, velocity };
    }

    /** 获取最大蓄力时间 */
    get maxChargeTime(): number {
        return this._maxChargeTime;
    }

    /** 重置冷却和蓄力 */
    public resetCooldown(): void {
        super.resetCooldown();
        this._isCharging = false;
        this._chargeTime = 0;
    }

    /**
     * 实现抽象方法：执行攻击动作
     * 由AttackingState调用
     */
    public performAttack(): void {
        // 直接调用现有的 attack 方法
        this.attack();
    }
}