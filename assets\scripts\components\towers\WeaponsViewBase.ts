import { _decorator, Component, Node, Prefab, Vec3, instantiate, director, Enum } from 'cc';
 
import { WeaponsBase, WeaponState } from '../../entities/weapons/WeaponsBase';
import { ErrorManager, ErrorSeverity } from '../../managers/ErrorManager';
const { ccclass, property } = _decorator;

/**
 * 发射器基类
 * 为各种发射器（如大炮、弓箭等）提供基础功能
 */
@ccclass('WeaponsViewBase')
export abstract class WeaponsViewBase extends Component {
    @property({ type: Node, displayName: '偏航轴节点', group: { name: '自身节点引用', id: '1' } })
    protected yawAxis: Node = null;

    @property({ type: Node, displayName: '俯仰轴节点', group: { name: '自身节点引用', id: '1' } })
    protected pitchAxis: Node = null;

    @property({ type: Node, displayName: '发射点节点', group: { name: '自身节点引用', id: '1' } })
    protected firePoint: Node = null;



    // 逻辑对象（武器塔实例）
    protected _logic: WeaponsBase | null = null;
    // 设置逻辑对象
    public setLogic(logic: WeaponsBase) {
        try {
            this._logic = logic;
            this.node.setWorldPosition(this._logic.position.x, this._logic.position.y, this._logic.position.z);
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.setLogic`
            );
        }
    }
    getPitch() {
        try {
            return this.pitchAxis.eulerAngles.x;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.getPitch`
            );
            return 0;
        }
    }
    setPitch(value: number) {
        try {
            this.pitchAxis.eulerAngles = new Vec3(value, 0, 0);
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.setPitch`
            );
        }
    }
    getYawAxis() {
        try {
            return this.yawAxis;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.getYawAxis`
            );
            return null;
        }
    }    getYaw() {
        try {
            return this.yawAxis.eulerAngles.y;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.getYaw`
            );
            return 0;
        }
    }
    setYaw(value: number) {
        try {
            this.yawAxis.eulerAngles = new Vec3(0, value, 0);
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.setYaw`
            );
        }
    }
    getFirePoint() {
        try {
            return this.firePoint;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.getFirePoint`
            );
            return null;
        }
    }
    getFirePosition() {
        try {
            return this.firePoint.worldPosition;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.getFirePosition`
            );
            return new Vec3();
        }
    }
    /**
      * 弹道显示状态变化时的处理
      * 子类可以重写此方法以实现特定的处理
      * @param visible 是否可见
      */
    protected onTrajectoryVisibilityChanged(_visible: boolean) {
        // 默认实现为空
    }
    /**
       * 速度
       */
    protected velocity: number = 0;

    /**
     * 方向向量
     */
    protected directionVec: Vec3 = new Vec3();

    /**
     * 当前目标位置
     */
    protected curTargetPos: Vec3 = new Vec3();

    /**
     * 主体位置
     */
    protected mainPosition: Vec3 = new Vec3();

    /**
     * 主体前方向
     */
    protected mainForward: Vec3 = new Vec3();

    /**
     * 主体上方向
     */
    protected mainUp: Vec3 = new Vec3();

    /**
     * 俯仰角
     */
    public get pitch() {
        return this.pitchAxis.eulerAngles.x;
    }
    protected set pitch(value: number) {
        this.pitchAxis.setRotationFromEuler(value, 0, 0);
    }

    /**
     * 偏航角
     */
    public get yaw() {
        return this.yawAxis.eulerAngles.y;
    }    protected set yaw(value: number) {
        try {
            this.yawAxis.setRotationFromEuler(0, value, 0);
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.set yaw`
            );
        }
    }
    
    updateView(dt: number) {
        try {
            // 子类实现具体更新逻辑
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.updateView`
            );
        }
    }

    /**
     * 视图恢复方法
     * 用于错误发生后的恢复操作
     */
    public recover(): void {
        try {
            // 重置视图状态
            if (this._logic) {
                // 重置位置
                this.node.setWorldPosition(
                    this._logic.position.x, 
                    this._logic.position.y, 
                    this._logic.position.z
                );
                
                // 重置旋转
                this.setYaw(0);
                this.setPitch(0);
            }
            
            ErrorManager.getInstance().logInfo(
                "武器视图已恢复",
                `WeaponsViewBase.recover`
            );
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `WeaponsViewBase.recover`
            );
        }
    }

    /**
     * 从错误中恢复
     * @param errorContext 错误上下文
     * @param severity 错误严重程度
     */
    public recoverFromError(errorContext: string, severity: ErrorSeverity): boolean {
        try {
            // 根据错误上下文选择合适的恢复策略
            if (errorContext.includes('setYaw') || errorContext.includes('setPitch')) {
                // 旋转错误 - 重置旋转
                this.resetRotation();
                return true;
            } 
            else if (errorContext.includes('Position')) {
                // 位置错误 - 重置位置
                this.resetPosition();
                return true;
            }
            else if (errorContext.includes('updateView')) {
                // 视图更新错误 - 完全重置
                this.recover();
                return true;
            }
            
            // 默认恢复策略
            return false;
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)), 
                `WeaponsViewBase.recoverFromError`
            );
            return false;
        }
    }
    
    /**
     * 重置旋转
     */
    private resetRotation(): void {
        try {
            if (this.yawAxis && this.yawAxis.isValid) {
                this.yawAxis.eulerAngles = new Vec3(0, 0, 0);
            }
            
            if (this.pitchAxis && this.pitchAxis.isValid) {
                this.pitchAxis.eulerAngles = new Vec3(0, 0, 0);
            }
            
            ErrorManager.getInstance().logInfo(
                '武器旋转已重置', 
                'WeaponsViewBase.resetRotation'
            );
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)), 
                `WeaponsViewBase.resetRotation`
            );
        }
    }
    
    /**
     * 重置位置
     */
    private resetPosition(): void {
        try {
            if (this._logic && this.node && this.node.isValid) {
                this.node.setWorldPosition(
                    this._logic.position.x, 
                    this._logic.position.y, 
                    this._logic.position.z
                );
                
                ErrorManager.getInstance().logInfo(
                    '武器位置已重置', 
                    'WeaponsViewBase.resetPosition'
                );
            }
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)), 
                `WeaponsViewBase.resetPosition`
            );
        }
    }
}