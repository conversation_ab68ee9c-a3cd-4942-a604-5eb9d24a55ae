
import { Character } from '../entities/Character';
import { WeaponsBase } from '../entities/weapons/WeaponsBase';
import { ProjectileCore } from '../entities/projectiles/ProjectileCore';
import { EnemyBase } from '../entities/enemies/EnemyBase';

export interface IProjectileManager {
    /**
     * 将投射物添加到对象池
     * @param type 投射物类型标识符
     * @param projectile 要添加到对象池的投射物
     */
    addToPool(type: string, projectile: ProjectileCore): void;

    /**
     * 从对象池获取投射物
     * @param type 投射物类型标识符
     * @returns 投射物实例，如果对象池中没有可用对象则返回null
     */
    getFromPool(type: string): ProjectileCore | null;

    /**
     * 创建或获取投射物
     * 优先从对象池获取，如果对象池中没有可用对象，则使用工厂函数创建新的投射物
     * @param type 投射物类型标识符
     * @param factory 创建投射物的工厂函数，当对象池中没有可用对象时调用
     * @returns 投射物实例
     */
    createOrGetProjectile(type: string, factory: () => ProjectileCore): ProjectileCore;

    /**
     * 添加投射物
     * @param projectile 要添加的投射物
     */
    addProjectile(projectile: ProjectileCore): void;

    /**
     * 移除投射物
     * @param projectile 要移除的投射物
     */
    removeProjectile(projectile: ProjectileCore): void;

    /**
     * 回收投射物到对象池
     * @param projectile 要回收的投射物
     */
    recycleProjectile(projectile: ProjectileCore): void;

    /**
     * 更新所有投射物
     * @param deltaTime 时间增量
     * @param targets 可选的目标列表，用于碰撞检测
     */
    update(deltaTime: number, targets?: any[]): void;

    /**
     * 获取所有活跃的投射物
     * @returns 活跃的投射物数组
     */
    getActiveProjectiles(): ProjectileCore[];

    /**
     * 清空所有投射物和对象池
     */
    clear(): void;
}

export interface IGameManager {
    /**
     * 添加投射物
     * @param projectile 投射物实例
     * @param type 投射物类型，用于对象池管理
     */
    addProjectile(projectile: ProjectileCore, type?: string): void;

    /**
     * 移除投射物并回收到对象池
     * @param projectile 要移除的投射物
     * @param type 投射物类型，用于对象池管理
     */
    removeProjectile(projectile: ProjectileCore, type?: string): void;

    /**
     * 创建或获取投射物
     * 优先从对象池获取，如果对象池中没有可用对象，则使用工厂函数创建新的投射物
     * @param type 投射物类型标识符
     * @param factory 创建投射物的工厂函数，当对象池中没有可用对象时调用
     * @returns 投射物实例
     */
    createProjectile(type: string, factory: () => ProjectileCore): ProjectileCore;

    /**
     * 添加武器
     * @param character 武器实例
     */
    addWeapon(character: WeaponsBase): void;

    /**
     * 添加敌人
     * @param createEnemyFn 创建敌人的工厂函数
     * @returns 敌人实例
     */
    addEnemy(createEnemyFn: () => Character): Character;

    /**
     * 移除敌人
     * @param enemy 要移除的敌人
     */
    removeEnemy(enemy: EnemyBase): void;

    /**
     * 更新游戏状态
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;

    /**
     * 获取所有武器
     * @returns 武器数组
     */
    getCharacters(): WeaponsBase[];
}

export interface IBattleManager {
    /**
     * 开始战斗
     */
    startBattle(): void;

    /**
     * 结束战斗
     * @param isVictory 是否胜利
     */
    endBattle(isVictory: boolean): void;

    /**
     * 处理攻击
     * @param attacker 攻击者
     * @param target 目标
     * @param damage 伤害值
     */
    handleAttack(attacker: Character, target: Character, damage: number): void;

    /**
     * 更新战斗状态
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void;

    /**
     * 检查战斗是否结束
     * @returns 是否结束
     */
    isBattleEnded(): boolean;
}