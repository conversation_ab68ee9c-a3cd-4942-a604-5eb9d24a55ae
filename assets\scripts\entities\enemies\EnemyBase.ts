import { Vec3, instantiate, Prefab, Node } from 'cc';
import { Character, Faction, CharacterType } from '../Character';
import { EnemyViewBase } from '../../components/enemies/EnemyViewBase';
import { StateMachine } from '../../core/StateMachine';
import { EnemyIdleState, EnemyDeathState, EnemyAttackingState, EnemyHurtState } from './EnemyStates';

export abstract class EnemyBase extends Character {
    private _path: Vec3[]; // 移动路径
    private _currentPathIndex: number = 0; // 当前路径点索引
    private _reward: number; // 击败奖励
    private _view: EnemyViewBase | null = null;
    // 临时变量，用于计算
    private _attackRange: number = 1.5; // 攻击范围，单位可根据实际调整
    private _target: Character | null = null; // 当前攻击目标
    private _gameManager: any;
    private _lastPosition: { x: number, y: number, z: number } | null = null;
    private _stateMachine: StateMachine;
    private _isBeingRemoved: boolean = false; // 标记是否正在被移除，防止重复移除

    /** 死亡时回调方法 */
    public onDefeated?: () => void;

    constructor(
        path: Vec3[],

        maxHealth: number,
        attackSpeed: number,
        attackDamage: number,
        defense: number,
        moveSpeed: number,
        reward: number,
        prefab: Prefab,
        parent: Node,
        gameManager: any, // 新增
        attackRange: number = 1.5 // 新增参数，默认1.5
    ) {
        super(Faction.ENEMY, CharacterType.INFANTRY, maxHealth, attackSpeed, attackDamage, defense, moveSpeed,);
        this._path = []
        path.forEach(element => {
            this._path.push(element.clone())
        });

        // 设置初始位置为第一个路径点
        if (this._path.length > 0) {
            const startPoint = this._path[0];
            this.setPosition(startPoint.x, startPoint.y, startPoint.z);
            console.log(`敌人初始位置设置为: (${startPoint.x}, ${startPoint.y}, ${startPoint.z})`);
        }

        this._reward = reward;
        this._view = this.createView(prefab, parent);
        this._attackRange = attackRange;
        this._gameManager = gameManager;
        this._stateMachine = this.createStateMachine();
    }

    public createView(prefab: Prefab, parent: Node): EnemyViewBase | null {
        if (!this._view) {
            const node = instantiate(prefab);
            if (!node) {
                console.error('Failed to instantiate prefab.');
                return null;
            }
            const view = node.getComponent(EnemyViewBase);
            if (!view) {
                console.error('Failed to get EnemyView component from prefab.');
                return null;
            }
            view.node.parent = parent;
            view.setLogic(this);
            return view;
        } else {
            this._view.node.parent = parent;
            this._view.setLogic(this);
            return this._view;
        }

    }    /**
     * 初始化并注册所有敌人状态到通用状态机
     */
    protected createStateMachine(): StateMachine {
        const sm = new StateMachine();
        sm.addState(new EnemyIdleState(this), true); // 默认空闲
        sm.addState(new EnemyAttackingState(this));  // 攻击
        sm.addState(new EnemyHurtState(this));       // 受击
        sm.addState(new EnemyDeathState(this));      // 死亡
        return sm;
    }

    /**
     * 切换状态
     * @param stateName 状态名称
     */
    public changeState(stateName: string): void {
        if (this._stateMachine) {
            this._stateMachine.changeState(stateName);
        }
    }

    /**
     * 状态更新统一接口
     */
    public updateState(dt: number): void {
        if (this._stateMachine) {
            this._stateMachine.update(dt);
        }
    }

    /**
     * 获取当前状态名
     */
    public get currentStateName(): string {
        return this._stateMachine ? this._stateMachine.stateName : 'none';
    }

    /**
     * 敌人死亡时的处理逻辑
     * 由Character.takeDamage调用
     */
    protected onDeath(): void {
        // 防止重复处理死亡
        if (this._isBeingRemoved) {
            console.log(`敌人 ${this.id} 已经在处理死亡流程中，跳过重复处理`);
            return;
        }

        // 设置为死亡状态
        this._isAlive = false;
        this._isBeingRemoved = true;

        // 切换到死亡状态
        this.changeState('dead');

        // 触发死亡回调
        this.onDefeat();

        // 通知视图层
        if (this._view) {
            this._view.onDead();
        }

        // 通知游戏管理器移除敌人
        if (this._gameManager) {
            // 延迟移除，让死亡动画有时间播放
            setTimeout(() => {
                // 再次检查是否已被移除，避免重复移除
                if (this._isBeingRemoved) {
                    this._gameManager.removeEnemy(this);
                }
            }, 2000);
        }

        console.log(`敌人 ${this.id} 已死亡`);
    }

    /**
     * 敌人受击处理
     * 重写Character.takeDamage方法
     */
    public takeDamage(damage: number): void {
        // 如果已经死亡，不处理伤害
        if (!this.isAlive) return;

        // 计算实际伤害
        const actualDamage = Math.max(1, damage - this._defense);
        this._currentHealth -= actualDamage;

        console.log(`敌人 ${this.id} 受到 ${actualDamage} 点伤害，剩余生命值: ${this._currentHealth}/${this._maxHealth}`);

        // 更新视图层
        if (this._view) {
            this._view.playHitEffect();
        }

        // 如果生命值归零，触发死亡
        if (this._currentHealth <= 0) {
            this._currentHealth = 0;
            this.onDeath();
        } else {
            // 如果不是在攻击状态，切换到受击状态
            if (this.currentStateName !== 'attacking') {
                // 临时切换到受击状态，然后回到之前的状态
                const prevState = this.currentStateName;
                this.changeState('hurt');

                // 短暂延迟后恢复之前的状态
                setTimeout(() => {
                    if (this.isAlive && this.currentStateName === 'hurt') {
                        this.changeState(prevState);
                    }
                }, 300);
            }
        }
    }

    protected onUpdate(deltaTime: number): void {
        // 1. 表现层同步等通用逻辑
        if (this._view) {
            this._view.updateView(deltaTime);
        }
        // ...可扩展其他通用逻辑...
        // 注意：moveUpdate 现在在 update 方法中统一调用，这里不再调用
    }

    /**
     * 敌人攻击逻辑
     */
    public attack(): void {
        // 如果已经死亡，不执行攻击
        if (!this.isAlive) return;

        console.log(`敌人 ${this.id} 发起攻击`);

        // 获取当前目标
        // 注意：这里假设目标已经通过setTarget方法设置
        if (this._target && this._target.isAlive) {
            // 计算伤害
            const damage = this._attackDamage;

            // 对目标造成伤害
            this._target.takeDamage(damage);

            console.log(`敌人 ${this.id} 对目标 ${this._target.id} 造成 ${damage} 点伤害`);

            // 播放攻击动画
            if (this._view) {
                // 这里可以添加攻击动画播放逻辑
            }
        } else {
            // 如果没有有效目标，切换回空闲状态
            this.changeState('idle');
        }
    }

    // 每帧更新
    public update(deltaTime: number): void {
        // 更新状态机
        this.updateState(deltaTime);

        // 调用通用更新逻辑
        this.onUpdate(deltaTime);
        console.log("enemy update",this.isAlive ,this.currentStateName)
        // 确保移动逻辑被执行（即使不在idle状态）
        if (this.isAlive && this.currentStateName !== 'dead') {
            this.moveUpdate(deltaTime);

            // 调试信息
            console.log(`敌人位置: (${this.position.x.toFixed(2)}, ${this.position.y.toFixed(2)}, ${this.position.z.toFixed(2)})`);
        }
    }

    /**
     * 检查是否有目标进入攻击范围
     * 可根据实际目标类型扩展
     */
    checkIfAbleAttack(): boolean {
        // 这里以检测最近的敌对单位为例，实际应由场景管理器或传入目标列表
        // 假设 this._target 已经被外部赋值为最近的敌对单位
        if (!this._target) return false;
        // 判断目标是否死亡，兼容通用 StateMachine
        if ('isAlive' in this._target && !this._target.isAlive) return false;
        const dist = Vec3.distance(this.position, this._target.position);
        return dist <= this._attackRange;
    }

    /**
     * 设置当前攻击目标
     */
    public setTarget(target: Character | null): void {
        this._target = target;
    }    // 沿路径移动
    public moveUpdate(dt: number): void {
        // 如果敌人处于死亡状态，不执行移动
        if (this.currentStateName === 'dead' || !this.isAlive) return;

        // 存储上一帧位置，用于计算速度
        this._lastPosition = { ...this.position };

        // 如果有设置路径，就沿着路径移动
        if (this._path && this._path.length > 0) {
            const targetPos = this._path[this._currentPathIndex];
            const currentPos = new Vec3(this._position.x, this._position.y, this._position.z);
            const distance = Vec3.distance(currentPos, targetPos);

            // 调试信息
            console.log(`当前路径点: ${this._currentPathIndex}, 目标: (${targetPos.x}, ${targetPos.y}, ${targetPos.z}), 距离: ${distance.toFixed(2)}`);

            // 如果达到当前路径点，移动到下一个路径点
            if (distance < 0.5) {
                this._currentPathIndex++;
                console.log(`到达路径点，切换到下一点: ${this._currentPathIndex}`);

                // 如果到达终点，可以触发某些事件
                if (this._currentPathIndex >= this._path.length) {
                    console.log(`到达最后一个路径点，触发终点事件`);
                    this._currentPathIndex = 0; // 循环路径
                    // 触发敌人到达终点事件
                    this.onReachDestination();
                }

                // 立即更新目标位置为新的路径点
                return;
            } else {
                // 计算移动方向
                const direction = new Vec3();
                Vec3.subtract(direction, targetPos, currentPos);
                direction.normalize();

                // 应用移动速度
                const moveDistance = this._moveSpeed * dt;
                const movement = new Vec3(
                    direction.x * moveDistance,
                    direction.y * moveDistance,
                    direction.z * moveDistance
                );

                console.log(`移动距离: ${moveDistance.toFixed(2)}, 方向: (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)}, ${direction.z.toFixed(2)})`);

                // 更新位置
                this.setPosition(
                    this._position.x + movement.x,
                    this._position.y + movement.y,
                    this._position.z + movement.z
                );

                // 更新视图
                if (this._view) {
                    this._view.updatePosition(this._position);
                    this._view.updateRotation(Math.atan2(direction.z, direction.x) * 180 / Math.PI);
                }
            }
        }
    }

    // 到达终点 - 当敌人到达终点时调用
    protected onReachDestination(): void {
        this.changeState('dead');
        // 扣除玩家生命值或其他逻辑
    }

    // 被击败时触发奖励
    public onDefeat(): void {
        // 触发奖励逻辑，例如增加金币
        console.log(`Enemy defeated! Reward: ${this._reward}`);

        // 如果设置了回调，则调用它
        if (typeof this.onDefeated === 'function') {
            this.onDefeated();
        }
    }    /**
     * 重置敌人状态（对象池复用时调用）
     */
    public reset(): void {
        // 重置生命值
        this._currentHealth = this._maxHealth;
        this._isAlive = true;

        // 重置移除标记
        this._isBeingRemoved = false;

        // 重置路径索引
        this._currentPathIndex = 0;

        // 重置目标
        this._target = null;

        // 重置状态机
        if (this._stateMachine) {
            // 强制重置状态机到初始状态
            (this._stateMachine as any).resetToInitialState();
        } else {
            // 如果状态机不存在，创建一个新的
            this._stateMachine = this.createStateMachine();
        }

        // 重置视图
        if (this._view) {
            // 重置位置
            this._view.updatePosition(this.position);

            // 重置血条
            this._view.updateHealthBar();
        }

        console.log(`敌人 ${this.id} 已重置`);
    }
    // 获取当前移动速度向量
    public getVelocityVector(): { x: number, y: number, z: number } {
        // 速度 = 本帧位置 - 上一帧位置
        if (!this._lastPosition) return { x: 0, y: 0, z: 0 };
        return {
            x: this.position.x - this._lastPosition.x,
            y: this.position.y - this._lastPosition.y,
            z: this.position.z - this._lastPosition.z
        };
    }

    /**
     * 获取敌人视图组件
     * @returns 敌人视图组件
     */
    public getView(): EnemyViewBase | null {
        return this._view;
    }
}