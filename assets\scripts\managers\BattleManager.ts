import { _decorator, Component } from 'cc';
import { Character } from '../entities/Character';
import { EnemyBase } from '../entities/enemies/EnemyBase';
import { IGameManager } from '../interfaces/IManager';
import { IBattleManager, IProjectileManager } from '../interfaces/IManager';
import { GameEvent, GameEventMap } from '../events/GameEvents';
import { EventManager } from './EventManager';
import { WeaponsBase } from '../entities/weapons/WeaponsBase';
import { List } from '../utils/List';
import { QuadTree, Bounds } from '../utils/QuadTree';
import { ErrorManager, LogicError } from './ErrorManager';
import { ProjectileCore, ProjectileMode } from '../entities/projectiles/ProjectileCore';

const { ccclass, property } = _decorator;

// 简单对象池实现
class EnemyPool {
    private _pool: EnemyBase[] = [];
    get(): EnemyBase | null {
        return this._pool.pop() || null;
    }
    put(enemy: EnemyBase) {
        // 可重置敌人状态
        enemy.reset && enemy.reset();
        this._pool.push(enemy);
    }
}

/**
 * 战斗管理器
 * 负责管理战斗相关的逻辑和状态
 */
@ccclass('BattleManager')
export class BattleManager implements IBattleManager {
    private _isBattleActive: boolean = false;
    private _battleStartTime: number = 0;
    private _battleDuration: number = 0;
    private _gameManager: IGameManager = null;
    private _eventManager: EventManager<GameEventMap>;
    private _projectileManager: IProjectileManager = null;

    // 战斗单位管理
    private _weapons: List<WeaponsBase> = new List<WeaponsBase>();
    private _enemys: List<EnemyBase> = new List<EnemyBase>();
    private _enemyPool: EnemyPool = new EnemyPool();

    // 四叉树，用于空间查询
    private _quadTree: QuadTree<EnemyBase>;

    // 游戏世界边界
    private _worldBounds: Bounds = {
        x: 0,       // 世界中心点 x 坐标
        z: 0,       // 世界中心点 z 坐标
        width: 1000, // 世界宽度
        height: 1000 // 世界高度
    };
    /**
     * 初始化战斗管理器
     * @param eventManager 事件管理器实例
     */
    init(eventManager?: EventManager<GameEventMap>) {
        // 初始化四叉树
        this._quadTree = new QuadTree<EnemyBase>(this._worldBounds);

        // 设置事件管理器
        if (eventManager) {
            this._eventManager = eventManager;
        }

        // 确保错误管理器已初始化
        if (this._eventManager) {
            ErrorManager.getInstance().init(this._eventManager);
        }
    }

    /**
     * 设置游戏管理器
     * @param gameManager 游戏管理器实例
     */
    setGameManager(gameManager: IGameManager): void {
        this._gameManager = gameManager;
    }

    /**
     * 设置投射物管理器
     * @param projectileManager 投射物管理器实例
     */
    setProjectileManager(projectileManager: IProjectileManager): void {
        this._projectileManager = projectileManager;
    }

    /**
     * 设置游戏世界边界
     * @param bounds 世界边界
     */
    public setWorldBounds(bounds: Bounds): void {
        this._worldBounds = bounds;
        // 重新初始化四叉树
        this._quadTree = new QuadTree<EnemyBase>(this._worldBounds);
        // 重新插入所有敌人
        this.rebuildQuadTree();
    }

    /**
     * 重建四叉树
     */
    private rebuildQuadTree(): void {
        this._quadTree.clear();
        for (const enemy of this._enemys.elements) {
            if (enemy.isAlive) {
                this._quadTree.insert(enemy);
            }
        }
    }

    /**
     * 添加武器
     * @param weapon 武器实例
     */
    public addWeapon(weapon: WeaponsBase): void {
        this._weapons.push(weapon);
    }

    /**
     * 移除武器
     * @param weapon 要移除的武器
     */
    public removeWeapon(weapon: WeaponsBase): void {
        this._weapons.remove(weapon);
    }

    /**
     * 添加敌人
     * @param createEnemyFn 创建敌人的工厂函数
     * @returns 敌人实例
     */
    public addEnemy(createEnemyFn: () => EnemyBase): EnemyBase {
        let enemy = this._enemyPool.get();
        if (!enemy) {
            enemy = createEnemyFn();
        }
        this._enemys.push(enemy);
        return enemy;
    }

    /**
     * 移除敌人
     * @param enemy 要移除的敌人
     */
    public removeEnemy(enemy: EnemyBase): void {
        this._enemys.remove(enemy);
        this._enemyPool.put(enemy);
    }

    /**
     * 获取所有武器
     * @returns 武器数组
     */
    public getWeapons(): WeaponsBase[] {
        return this._weapons.elements;
    }

    /**
     * 查询指定圆形范围内的敌人
     * @param center 圆心坐标
     * @param radius 半径
     * @returns 范围内的敌人数组
     */
    public queryEnemiesInRange(center: { x: number, z: number }, radius: number): EnemyBase[] {
        return this._quadTree.queryCircle(center, radius);
    }

    /**
     * 获取武器攻击范围内的敌人
     * @param weapon 武器
     * @param attackRange 攻击范围
     * @returns 范围内的敌人数组
     */
    public getEnemiesInWeaponRange(weapon: WeaponsBase, attackRange: number): EnemyBase[] {
        const position = weapon.position;
        return this.queryEnemiesInRange({ x: position.x, z: position.z }, attackRange);
    }

    /**
     * 开始战斗
     */
    public startBattle(): void {
        if (this._isBattleActive) return;

        this._isBattleActive = true;
        this._battleStartTime = Date.now();
        this._battleDuration = 0;

        // 发送战斗开始事件
        this._eventManager.emit(GameEvent.BATTLE_START, {
            timestamp: this._battleStartTime
        });
    }

    /**
     * 结束战斗
     * @param isVictory 是否胜利
     */
    public endBattle(isVictory: boolean): void {
        if (!this._isBattleActive) return;

        this._isBattleActive = false;
        this._battleDuration = Date.now() - this._battleStartTime;

        // 发送战斗结束事件
        this._eventManager.emit(GameEvent.BATTLE_END, {
            isVictory,
            duration: this._battleDuration
        });
    }

    /**
     * 处理攻击
     * @param attacker 攻击者
     * @param target 目标
     * @param damage 伤害值
     */
    public handleAttack(attacker: Character, target: Character, damage: number): void {
        if (!this._isBattleActive) return;

        // 计算实际伤害
        const actualDamage = this.calculateDamage(attacker, target, damage);

        // 应用伤害
        target.takeDamage(actualDamage);

        // 发送攻击事件
        this._eventManager.emit(GameEvent.ATTACK, {
            attacker: attacker.id,
            target: target.id,
            damage: actualDamage
        });

        // 检查目标是否死亡
        if (!target.isAlive) {
            this.handleUnitDeath(target);
        }
    }

    /**
     * 计算实际伤害
     * @param attacker 攻击者
     * @param target 目标
     * @param baseDamage 基础伤害
     * @returns 实际伤害值
     */
    private calculateDamage(_attacker: Character, _target: Character, baseDamage: number): number {
        // 这里可以添加伤害计算公式
        // 例如：考虑攻击力、防御力、暴击等
        return baseDamage;
    }

    /**
     * 处理单位死亡
     * @param unit 死亡的单位
     */
    private handleUnitDeath(unit: Character): void {
        if (unit instanceof EnemyBase) {
            // 处理敌人死亡
            this.removeEnemy(unit);
        }

        // 发送单位死亡事件
        this._eventManager.emit(GameEvent.UNIT_DEATH, {
            unitId: unit.id,
            unitType: unit.constructor.name
        });

        // 检查战斗是否结束
        this.checkBattleEnd();
    }

    /**
     * 检查战斗是否结束
     */
    private checkBattleEnd(): void {
        const weapons = this.getWeapons();
        const hasAliveWeapons = weapons.some(weapon => weapon.isAlive);

        if (!hasAliveWeapons) {
            // 所有武器都被摧毁，战斗失败
            this.endBattle(false);
        }
    }    /**
     * 更新战斗状态
     * @param deltaTime 时间增量
     */
    public update(deltaTime: number): void {
        try {
            // 确保战斗状态是激活的
            if (!this._isBattleActive) {
                // 检查事件管理器是否已初始化
                if (!this._eventManager) {
                    console.error("BattleManager: 事件管理器未初始化，无法启动战斗");
                    return;
                }
                // 自动激活战斗状态，方便测试
                this.startBattle();
            }

            // 更新战斗时间
            this._battleDuration = Date.now() - this._battleStartTime;


            // 检查战斗是否超时
            /* if (this._battleDuration > 300000) { // 5分钟超时
                this.endBattle(false);
                return;
            } */

            // 获取所有活跃的单位
            const activeEnemys = this._enemys.filter((enemy: EnemyBase) => enemy.isAlive);

            const activeWeapons = this._weapons.filter((weapon: WeaponsBase) => weapon.isAlive);

            // 重建四叉树
            this.rebuildQuadTree();

            // 输出调试信息
            console.log(`BattleManager: 战斗时间 ${this._battleDuration}ms, 敌人总数: ${this._enemys.count}, 活跃敌人: ${activeEnemys.length}, 武器总数: ${this._weapons.count}, 活跃武器: ${activeWeapons.length}`);            // 更新敌人
            for (const enemy of activeEnemys) {
                try {
                    enemy.update(deltaTime);
                } catch (error) {
                    ErrorManager.getInstance().logError(
                        error instanceof Error ? error : new Error(String(error)),
                        `BattleManager.updateEnemy(${enemy.constructor.name})`
                    );
                    // 尝试恢复 - 例如移除问题敌人
                    this.tryRecoverFromEnemyError(enemy);
                }
            }
            
            // 处理数学模式下的投射物碰撞检测和伤害计算
            this.handleMathModeProjectileCollisions(deltaTime);

            // 更新武器
            for (const weapon of activeWeapons) {
                if (weapon.isAlive) {
                    try {
                        // 使用四叉树查询攻击范围内的敌人
                        const attackRange = weapon.attackRange;
                        const enemiesInRange = this.getEnemiesInWeaponRange(weapon, attackRange);

                        console.log(`武器 ${weapon.constructor.name} 攻击范围内有 ${enemiesInRange.length} 个敌人`);

                        // 设置目标并更新武器
                        weapon.setTargets(enemiesInRange);
                        weapon.update(deltaTime);
                    } catch (error) {
                        ErrorManager.getInstance().logError(
                            error instanceof Error ? error : new Error(String(error)),
                            `BattleManager.updateWeapon(${weapon.constructor.name})`
                        );
                        // 尝试恢复 - 例如重置武器状态
                        this.tryRecoverFromWeaponError(weapon);
                    }
                }
            }

            // 处理数学模式下的投射物碰撞和伤害
            this.handleMathModeProjectileCollisions(deltaTime);
        } catch (error) {
            // 处理整体更新过程中的错误
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'BattleManager.update'
            );
        }
    }    /**
     * 尝试从敌人错误中恢复
     * @param enemy 出错的敌人
     */
    private tryRecoverFromEnemyError(enemy: EnemyBase): void {
        try {
            // 如果敌人已经死亡，移除它
            if (!enemy.isAlive) {
                this.removeEnemy(enemy);
                return;
            }

            // 获取敌人视图
            const view = enemy.getView();
            if (view) {
                // 使用我们添加的恢复功能
                const recovered = view.recover();
                ErrorManager.getInstance().logInfo(
                    `敌人视图恢复${recovered ? '成功' : '尝试中'}`,
                    'BattleManager.tryRecoverFromEnemyError'
                );
            }

            // 尝试重置敌人逻辑状态
            if (enemy.reset) {
                enemy.reset();
                ErrorManager.getInstance().logInfo(
                    '敌人逻辑状态已重置',
                    'BattleManager.tryRecoverFromEnemyError'
                );
            }
        } catch (error) {
            // 如果恢复也失败了，记录并移除
            ErrorManager.getInstance().logWarning(
                `无法恢复敌人错误，将移除敌人: ${error.message}`,
                'BattleManager.tryRecoverFromEnemyError'
            );
            this.removeEnemy(enemy);
        }
    }

    /**
     * 尝试从武器错误中恢复
     * @param weapon 出错的武器
     */
    private tryRecoverFromWeaponError(weapon: WeaponsBase): void {
        try {
            // 重置武器目标
            weapon.setTargets([]);

            // 尝试将武器恢复到空闲状态
            if (weapon.changeState) {
                weapon.changeState('idle');
            }
        } catch (error) {
            // 如果恢复也失败了，记录错误
            ErrorManager.getInstance().logWarning(
                `无法恢复武器错误: ${error.message}`,
                'BattleManager.tryRecoverFromWeaponError'
            );
        }
    }

    /**
     * 检查战斗是否结束
     * @returns 是否结束
     */
    public isBattleEnded(): boolean {
        return !this._isBattleActive;
    }

    /**
     * 处理数学模式下的投射物碰撞和伤害
     * 通过检测投射物和敌人之间的距离来判断是否发生碰撞
     * @param deltaTime 时间增量
     */
    private handleMathModeProjectileCollisions(deltaTime: number): void {
        // 确保投射物管理器已初始化
        if (!this._projectileManager) {
            return;
        }

        // 获取所有活跃的投射物
        const activeProjectiles = this._projectileManager.getActiveProjectiles();
        
        // 获取所有活跃的敌人
        const activeEnemies = this._enemys.filter((enemy: EnemyBase) => enemy.isAlive);
        
        if (activeProjectiles.length === 0 || activeEnemies.length === 0) {
            return;
        }

        // 优化：每帧只处理数学模式下的投射物
        for (const projectile of activeProjectiles) {
            // 只处理数学模式下的投射物
            if (projectile.mode !== ProjectileMode.MATH || !projectile.isActive) {
                continue;
            }

            // 获取投射物的位置和碰撞半径
            const projectilePos = projectile.position;
            // 默认碰撞半径为1.0，也可以从投射物获取
            const collisionRadius = 1.0;
            
            // 对每个敌人检查距离
            for (const enemy of activeEnemies) {
                if (!enemy.isAlive) {
                    continue;
                }

                const enemyPos = enemy.position;
                
                // 计算投射物和敌人之间的平面距离（忽略y轴高度差异）
                const dx = projectilePos.x - enemyPos.x;
                const dz = projectilePos.z - enemyPos.z;
                const distanceSquared = dx * dx + dz * dz;
                
                // 敌人碰撞半径（可以根据敌人类型调整）
                const enemyRadius = 1.0;
                
                // 如果距离小于两者半径之和，则视为碰撞
                const minDistanceSquared = (collisionRadius + enemyRadius) * (collisionRadius + enemyRadius);
                
                if (distanceSquared <= minDistanceSquared) {
                    // 如果发生碰撞且投射物未被触发过
                    // 获取子弹伤害（如果是BulletCore类型）
                    let damage = 10; // 默认伤害
                    if ('damage' in projectile) {
                        damage = (projectile as any).damage;
                    }
                    
                    // 记录日志
                    console.log(`数学模式碰撞：投射物 ${projectile.constructor.name} 击中敌人 ${enemy.id}，造成 ${damage} 点伤害`);
                    
                    // 对敌人造成伤害
                    enemy.takeDamage(damage);
                    
                    // 处理投射物碰撞后的逻辑（例如爆炸或穿透）
                    // 如果有onCollision方法，调用它
                    if (typeof projectile.onCollision === 'function') {
                        projectile.onCollision(projectilePos);
                    } else {
                        // 如果没有特定的碰撞处理方法，直接销毁投射物
                        projectile.destroy();
                    }
                    
                    // 如果敌人死亡，处理敌人死亡逻辑
                    if (!enemy.isAlive) {
                        this.handleUnitDeath(enemy);
                    }
                    
                    // 如果投射物不支持穿透，跳出敌人循环
                    break;
                }
            }
        }
    }
}