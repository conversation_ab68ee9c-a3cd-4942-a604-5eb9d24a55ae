import { _decorator, sys } from 'cc';
import { ErrorSeverity } from '../managers/ErrorManager';

/**
 * 日志文件管理器
 * 负责将错误和日志信息保存到本地文件
 */
export class LogFileManager {
    private static _instance: LogFileManager;
    private _logBuffer: string[] = [];
    private _maxBufferSize: number = 100;
    private _logFilePath: string = '';
    private _isEnabled: boolean = false;
    
    /**
     * 获取单例
     */
    public static getInstance(): LogFileManager {
        if (!this._instance) {
            this._instance = new LogFileManager();
        }
        return this._instance;
    }
    
    /**
     * 初始化日志管理器
     * @param enabled 是否启用日志文件
     */
    public init(enabled: boolean = true): void {
        this._isEnabled = !!(enabled && sys.isBrowser && typeof window !== 'undefined' && window.localStorage);
        if (this._isEnabled) {
            this._logFilePath = `shotgame_error_log_${this.getFormattedDate()}`;
            this.writeToLog('日志开始记录', 'INFO', 'LogFileManager.init');
            console.log('[LogManager] 初始化完成，日志文件：', this._logFilePath);
        }
    }
    
    /**
     * 获取格式化的日期字符串
     */
    private getFormattedDate(): string {
        const now = new Date();
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}`;
    }
    
    /**
     * 获取格式化的时间字符串
     */
    private getFormattedTime(): string {
        const now = new Date();
        return `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}.${String(now.getMilliseconds()).padStart(3, '0')}`;
    }
    
    /**
     * 将错误信息写入日志
     * @param message 错误消息
     * @param severity 错误严重程度
     * @param context 错误上下文
     */
    public writeToLog(message: string, severity: string, context: string): void {
        if (!this._isEnabled) return;
        const logEntry = `[${this.getFormattedTime()}][${severity}][${context}] ${message}`;
        this._logBuffer.push(logEntry);
        if (this._logBuffer.length >= this._maxBufferSize) {
            this.flushLogBuffer();
        }
    }
    
    /**
     * 将错误对象写入日志
     * @param error 错误对象
     * @param severity 错误严重程度
     * @param context 错误上下文
     */
    public writeErrorToLog(error: Error, severity: ErrorSeverity, context: string): void {
        const severityStr = typeof severity === 'string' ? severity : (ErrorSeverity[severity] || 'UNKNOWN');
        const message = `${error.name}: ${error.message}\nStack: ${error.stack || 'No stack trace available'}`;
        this.writeToLog(message, severityStr, context);
    }
    
    /**
     * 将缓冲区中的日志写入本地存储
     */
    private flushLogBuffer(): void {
        if (!this._isEnabled || this._logBuffer.length === 0) return;
        try {
            const existingLog = window.localStorage.getItem(this._logFilePath) || '';
            const newLogs = this._logBuffer.join('\n');
            const combinedLog = existingLog ? `${existingLog}\n${newLogs}` : newLogs;
            window.localStorage.setItem(this._logFilePath, combinedLog);
            this._logBuffer = [];
        } catch (error) {
            console.error('无法写入日志文件:', error);
            this._isEnabled = false;
        }
    }
    
    /**
     * 导出日志
     * 返回所有日志内容，可用于下载或发送到服务器
     */
    public exportLog(): string {
        if (!this._isEnabled) return '';
        this.flushLogBuffer();
        return window.localStorage.getItem(this._logFilePath) || '';
    }
    
    /**
     * 清除日志
     */
    public clearLog(): void {
        if (!this._isEnabled) return;
        this._logBuffer = [];
        window.localStorage.removeItem(this._logFilePath);
        this._logFilePath = `shotgame_error_log_${this.getFormattedDate()}`;
        this.writeToLog('日志已重置', 'INFO', 'LogFileManager.clearLog');
    }
    
    /**
     * 关闭并保存日志
     * 在游戏退出前调用，确保所有日志都已保存
     */
    public close(): void {
        if (!this._isEnabled) return;
        this.writeToLog('日志结束记录', 'INFO', 'LogFileManager.close');
        this.flushLogBuffer();
    }
}
