import { _decorator, Component, Node, UITransform, Color, Sprite } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 血条UI组件
 * 提供可视化的生命值显示
 */
@ccclass('HealthBarUI')
export class HealthBarUI extends Component {
    @property(Node)
    private barFill: Node = null; // 血条填充部分

    @property(Node)
    private barBackground: Node = null; // 血条背景

    @property(Color)
    private healthyColor: Color = new Color(0, 255, 0, 255); // 满血颜色（绿色）

    @property(Color)
    private criticalColor: Color = new Color(255, 0, 0, 255); // 低血颜色（红色）

    @property
    private criticalThreshold: number = 0.3; // 低血阈值（30%）

    @property
    private showDuration: number = 3; // 受击后显示时间（秒）

    private _maxWidth: number = 0; // 血条最大宽度
    private _currentRatio: number = 1; // 当前血量比例
    private _hideTimer: number = 0; // 隐藏计时器
    private _isVisible: boolean = false; // 是否可见

    onLoad() {
        // 初始化血条最大宽度
        if (this.barFill && this.barFill.getComponent(UITransform)) {
            this._maxWidth = this.barFill.getComponent(UITransform).width;
        }

        // 默认隐藏血条
        this.setVisible(false);
    }

    /**
     * 更新血条显示
     * @param currentHealth 当前生命值
     * @param maxHealth 最大生命值
     */
    public updateHealth(currentHealth: number, maxHealth: number): void {
        if (!this.barFill) return;

        // 计算血量比例
        this._currentRatio = Math.max(0, Math.min(1, currentHealth / maxHealth));

        // 更新血条宽度
        const fillTransform = this.barFill.getComponent(UITransform);
        if (fillTransform) {
            fillTransform.width = this._maxWidth * this._currentRatio;
        }

        // 更新血条颜色
        const fillSprite = this.barFill.getComponent(Sprite);
        if (fillSprite) {
            if (this._currentRatio <= this.criticalThreshold) {
                fillSprite.color = this.criticalColor;
            } else {
                fillSprite.color = this.healthyColor;
            }
        }

        // 显示血条并重置计时器
        this.setVisible(true);
        this._hideTimer = this.showDuration;
    }

    /**
     * 设置血条可见性
     * @param visible 是否可见
     */
    public setVisible(visible: boolean): void {
        if (this.node) {
            this.node.active = visible;
            this._isVisible = visible;
        }
    }

    /**
     * 强制显示血条
     */
    public forceShow(): void {
        this.setVisible(true);
    }

    /**
     * 强制隐藏血条
     */
    public forceHide(): void {
        this.setVisible(false);
        this._hideTimer = 0;
    }

    update(dt: number) {
        // 如果血条可见，更新隐藏计时器
        if (this._isVisible && this._hideTimer > 0) {
            this._hideTimer -= dt;
            if (this._hideTimer <= 0) {
                this.setVisible(false);
            }
        }
    }
}
