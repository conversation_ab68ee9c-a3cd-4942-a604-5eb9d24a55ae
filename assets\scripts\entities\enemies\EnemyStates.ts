import { IState } from '../../core/StateMachine';
import { EnemyBase } from './EnemyBase';
import { CharacterState } from '../Character';

/**
 * 敌人空闲状态
 */
export class EnemyIdleState implements IState {
    private owner: EnemyBase;

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'idle'; }

    onEnter(_prevState?: IState): void {
        // 空闲状态的进入逻辑
        console.log(`敌人 ${this.owner.id} 进入空闲状态`);
    }

    onUpdate(_dt: number): void {
        // 注意：移动逻辑现在在EnemyBase.update中统一处理
        // 不再需要在这里调用moveUpdate

        // 检查是否有可攻击目标
        if (this.owner.checkIfAbleAttack()) {
            this.owner.changeState('attacking');
        }
    }

    onExit(_nextState?: IState): void {
        // 退出空闲状态时可以执行的逻辑
    }

    canTransitionTo(_targetState: IState): boolean {
        // 空闲状态可以转换到任何其他状态
        return true;
    }
}

/**
 * 敌人攻击状态
 */
export class EnemyAttackingState implements IState {
    private owner: EnemyBase;
    private attackCooldown: number = 0;

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'attacking'; }

    onEnter(_prevState?: IState): void {
        // 重置攻击冷却
        this.attackCooldown = 0;
        console.log(`敌人 ${this.owner.id} 进入攻击状态`);
    }

    onUpdate(dt: number): void {
        // 攻击冷却计时
        this.attackCooldown -= dt;

        // 如果冷却结束，执行攻击
        if (this.attackCooldown <= 0) {
            this.owner.attack();
            // 设置攻击冷却时间（根据攻击速度属性）
            this.attackCooldown = 1 / this.owner.attackSpeed;
        }

        // 检查是否还能攻击（目标是否还在范围内）
        if (!this.owner.checkIfAbleAttack()) {
            this.owner.changeState('idle');
        }
    }

    onExit(_nextState?: IState): void {
        // 退出攻击状态的逻辑
        console.log(`敌人 ${this.owner.id} 退出攻击状态`);
    }

    canTransitionTo(_targetState: IState): boolean {
        // 攻击状态可以转换到空闲、受击或死亡状态
        return _targetState.name === 'idle' ||
               _targetState.name === 'hurt' ||
               _targetState.name === 'dead';
    }
}

/**
 * 敌人受击状态
 */
export class EnemyHurtState implements IState {
    private owner: EnemyBase;
    private hurtTimer: number = 0;
    private readonly hurtDuration: number = 0.3; // 受击状态持续时间（秒）

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'hurt'; }

    onEnter(_prevState?: IState): void {
        // 重置受击计时器
        this.hurtTimer = 0;

        // 可以在这里添加受击时的特殊逻辑
        console.log(`敌人 ${this.owner.id} 进入受击状态`);
    }

    onUpdate(dt: number): void {
        // 更新受击计时器
        this.hurtTimer += dt;

        // 如果受击时间结束，自动切换回空闲状态
        if (this.hurtTimer >= this.hurtDuration) {
            this.owner.changeState('idle');
        }
    }

    onExit(_nextState?: IState): void {
        // 退出受击状态的逻辑
        console.log(`敌人 ${this.owner.id} 退出受击状态`);
    }

    canTransitionTo(_targetState: IState): boolean {
        // 受击状态可以转换到任何状态
        return true;
    }
}

/**
 * 敌人死亡状态
 */
export class EnemyDeathState implements IState {
    private owner: EnemyBase;

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'dead'; }

    onEnter(_prevState?: IState): void {
        // 触发死亡回调
        console.log(`敌人 ${this.owner.id} 进入死亡状态`);

        // 注意：onDefeat已经在EnemyBase.onDeath中调用，这里不需要重复调用
    }

    onUpdate(_dt: number): void {
        // 死亡状态不需要更新
    }

    onExit(_nextState?: IState): void {
        // 敌人不应该从死亡状态退出
        console.warn(`警告：敌人 ${this.owner.id} 试图从死亡状态退出`);
    }

    canTransitionTo(_targetState: IState): boolean {
        // 死亡状态无法转换到其他状态
        return false;
    }
}
