import { IState } from '../../core/StateMachine';
import { EnemyBase } from './EnemyBase';

/**
 * 敌人空闲状态
 */
export class EnemyIdleState implements IState {
    private owner: EnemyBase;

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'idle'; }

    onEnter(prevState?: IState): void {
        // 空闲状态的进入逻辑
    }

    onUpdate(_dt: number): void {
        // 注意：移动逻辑现在在EnemyBase.update中统一处理
        // 不再需要在这里调用moveUpdate

        // 检查是否有可攻击目标
        if (this.owner.checkIfAbleAttack()) {
            this.owner.changeState('attacking');
        }
    }

    onExit(nextState?: IState): void {
        // 退出空闲状态时可以执行的逻辑
    }

    canTransitionTo(targetState: IState): boolean {
        // 空闲状态可以转换到任何其他状态
        return true;
    }
}

/**
 * 敌人攻击状态
 */
export class EnemyAttackingState implements IState {
    private owner: EnemyBase;
    private attackCooldown: number = 0;

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'attacking'; }

    onEnter(prevState?: IState): void {
        // 重置攻击冷却
        this.attackCooldown = 0;
    }

    onUpdate(dt: number): void {
        // 攻击冷却计时
        this.attackCooldown -= dt;

        // 如果冷却结束，执行攻击
        if (this.attackCooldown <= 0) {
            this.owner.attack();
            // 设置攻击冷却时间（根据攻击速度属性）
            this.attackCooldown = 1 / this.owner.attackSpeed;
        }

        // 检查是否还能攻击（目标是否还在范围内）
        if (!this.owner.checkIfAbleAttack()) {
            this.owner.changeState('idle');
        }
    }

    onExit(nextState?: IState): void {
        // 退出攻击状态的逻辑
    }

    canTransitionTo(targetState: IState): boolean {
        // 攻击状态可以转换到空闲或死亡状态
        return targetState.name === 'idle' || targetState.name === 'dead';
    }
}

/**
 * 敌人死亡状态
 */
export class EnemyDeathState implements IState {
    private owner: EnemyBase;

    constructor(owner: EnemyBase) {
        this.owner = owner;
    }

    get name() { return 'dead'; }
      onEnter(prevState?: IState): void {
        // 触发死亡回调
        this.owner.onDefeat();
    }

    onUpdate(dt: number): void {
        // 死亡状态不需要更新
    }

    onExit(nextState?: IState): void {
        // 敌人不应该从死亡状态退出
    }

    canTransitionTo(targetState: IState): boolean {
        // 死亡状态无法转换到其他状态
        return false;
    }
}
