import { Vector3, Vector3Util } from "./Vector3";

/** 用于弧度转角度 */
const rad2Deg = 180 / Math.PI;

/** 用于角度转弧度 */
const deg2Rad = Math.PI / 180;

/** 用于计算的临时工 */
const tempVec3: Vector3 = { x: 0, y: 0, z: 0 };

/**
 * 矢量工具
 */
export class VectorUtil {

    /**
     * 计算向量在指定平面上的投影
     * @param vector 被投影的向量
     * @param planeNormal 平面法线
     */
    public static projectOnPlane(vector: Vector3, planeNormal: Vector3, out?: Vector3): Vector3 {
        // 创建输出向量（如果未提供）
        const result = out || { x: 0, y: 0, z: 0 };

        // 使用点乘计算方向矢量在平面法线上的投影长度
        const projectionLength = Vector3Util.dot(vector, planeNormal);

        // 平面法线与长度相乘得到方向矢量在平面法线上的投影矢量
        Vector3Util.set(tempVec3, planeNormal.x, planeNormal.y, planeNormal.z);
        Vector3Util.multiplyScalar(tempVec3, tempVec3, projectionLength);

        // 方向矢量减去其在平面法线上的投影矢量即是其在平面上的投影矢量
        return Vector3Util.subtract(result, vector, tempVec3);
    }

    /**
     * 计算两个向量基于指定轴的夹角（逆时针方向为正方向，值范围 -180 ~ 180）
     * @param a 向量 a
     * @param b 向量 b
     * @param axis 参照轴向量（请确保是归一化的）
     */
    public static signedAngle(a: Vector3, b: Vector3, axis: Vector3): number {
        // 将向量 a 和 b 分别投影到以 axis 为法线的平面上
        const aOnAxisPlane = VectorUtil.projectOnPlane(a, axis);
        const bOnAxisPlane = VectorUtil.projectOnPlane(b, axis);

        // 归一化处理
        const aNormalized = Vector3Util.create();
        Vector3Util.normalize(aNormalized, aOnAxisPlane);

        const bNormalized = Vector3Util.create();
        Vector3Util.normalize(bNormalized, bOnAxisPlane);

        // 求出同时垂直于 a 和 b 的法向量
        const abNormal = Vector3Util.create();
        Vector3Util.cross(abNormal, aNormalized, bNormalized);
        Vector3Util.normalize(abNormal, abNormal);

        // 将法向量到 axis 上的投影长度
        // 若投影长度为正值（+1）则表示法向量与 axis 同向（向量叉乘的右手法则）
        const sign = Vector3Util.dot(abNormal, axis);

        // 求出向量 a 和 b 的夹角
        const radian = Math.acos(Vector3Util.dot(aNormalized, bNormalized));

        // 混合在一起！
        return radian * sign * rad2Deg;
    }

    // /**
    //  * 计算两个向量基于指定轴的夹角（逆时针方向为正方向，值范围 -180 ~ 180）
    //  * @param a 向量 a
    //  * @param b 向量 b
    //  * @param axis 参照轴向量（请确保是归一化的）
    //  */
    // public static signedAngle(a: Vector3, b: Vector3, axis: Vector3) {
    //     const n = Vector3Util.cross(Vector3Util.create(), a, b);
    //     const r = Math.atan2(Vector3Util.dot(n, axis), Vector3Util.dot(a, b));
    //     return -r * rad2Deg;
    // }

}
