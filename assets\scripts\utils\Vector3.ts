/**
 * 三维向量接口
 */
export interface Vector3 {
    x: number;
    y: number;
    z: number;
}

/**
 * 三维向量工具类
 */
export class Vector3Util {
    /**
     * 创建一个新的三维向量
     * @param x X 坐标
     * @param y Y 坐标
     * @param z Z 坐标
     * @returns 新的三维向量
     */
    public static create(x: number = 0, y: number = 0, z: number = 0): Vector3 {
        return { x, y, z };
    }

    /**
     * 复制一个三维向量
     * @param v 源向量
     * @returns 新的三维向量
     */
    public static clone(v: Vector3): Vector3 {
        return { x: v.x, y: v.y, z: v.z };
    }

    /**
     * 设置向量的值
     * @param out 目标向量
     * @param x X 坐标
     * @param y Y 坐标
     * @param z Z 坐标
     * @returns 目标向量
     */
    public static set(out: Vector3, x: number, y: number, z: number): Vector3 {
        out.x = x;
        out.y = y;
        out.z = z;
        return out;
    }

    /**
     * 复制向量的值
     * @param out 目标向量
     * @param v 源向量
     * @returns 目标向量
     */
    public static copy(out: Vector3, v: Vector3): Vector3 {
        out.x = v.x;
        out.y = v.y;
        out.z = v.z;
        return out;
    }

    /**
     * 向量加法
     * @param out 结果向量
     * @param a 向量 a
     * @param b 向量 b
     * @returns 结果向量
     */
    public static add(out: Vector3, a: Vector3, b: Vector3): Vector3 {
        out.x = a.x + b.x;
        out.y = a.y + b.y;
        out.z = a.z + b.z;
        return out;
    }

    /**
     * 向量减法
     * @param out 结果向量
     * @param a 向量 a
     * @param b 向量 b
     * @returns 结果向量
     */
    public static subtract(out: Vector3, a: Vector3, b: Vector3): Vector3 {
        out.x = a.x - b.x;
        out.y = a.y - b.y;
        out.z = a.z - b.z;
        return out;
    }

    /**
     * 向量乘以标量
     * @param out 结果向量
     * @param v 向量
     * @param s 标量
     * @returns 结果向量
     */
    public static multiplyScalar(out: Vector3, v: Vector3, s: number): Vector3 {
        out.x = v.x * s;
        out.y = v.y * s;
        out.z = v.z * s;
        return out;
    }

    /**
     * 向量点乘
     * @param a 向量 a
     * @param b 向量 b
     * @returns 点乘结果
     */
    public static dot(a: Vector3, b: Vector3): number {
        return a.x * b.x + a.y * b.y + a.z * b.z;
    }

    /**
     * 向量叉乘
     * @param out 结果向量
     * @param a 向量 a
     * @param b 向量 b
     * @returns 结果向量
     */
    public static cross(out: Vector3, a: Vector3, b: Vector3): Vector3 {
        const ax = a.x, ay = a.y, az = a.z;
        const bx = b.x, by = b.y, bz = b.z;

        out.x = ay * bz - az * by;
        out.y = az * bx - ax * bz;
        out.z = ax * by - ay * bx;
        return out;
    }

    /**
     * 计算向量长度
     * @param v 向量
     * @returns 向量长度
     */
    public static len(v: Vector3): number {
        return Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    }

    /**
     * 计算向量长度的平方
     * @param v 向量
     * @returns 向量长度的平方
     */
    public static lengthSquared(v: Vector3): number {
        return v.x * v.x + v.y * v.y + v.z * v.z;
    }

    /**
     * 归一化向量
     * @param out 结果向量
     * @param v 源向量
     * @returns 结果向量
     */
    public static normalize(out: Vector3, v: Vector3): Vector3 {
        const len = Vector3Util.len(v);
        if (len > 0) {
            const invLen = 1 / len;
            out.x = v.x * invLen;
            out.y = v.y * invLen;
            out.z = v.z * invLen;
        }
        return out;
    }

    /**
     * 计算向量在平面上的投影
     * @param out 结果向量
     * @param v 源向量
     * @param normal 平面法线
     * @returns 结果向量
     */
    public static projectOnPlane(out: Vector3, v: Vector3, normal: Vector3): Vector3 {
        // 计算向量在法线方向上的投影长度
        const dot = Vector3Util.dot(v, normal);
        
        // 创建一个临时向量，表示向量在法线方向上的投影
        const temp = Vector3Util.create();
        Vector3Util.multiplyScalar(temp, normal, dot);
        
        // 从原向量中减去法线方向的投影，得到平面上的投影
        return Vector3Util.subtract(out, v, temp);
    }

    /**
     * 计算两个向量之间的角度（弧度）
     * @param a 向量 a
     * @param b 向量 b
     * @returns 角度（弧度）
     */
    public static angle(a: Vector3, b: Vector3): number {
        const lenA = Vector3Util.len(a);
        const lenB = Vector3Util.len(b);
        
        if (lenA * lenB === 0) return 0;
        
        const dot = Vector3Util.dot(a, b);
        const cosine = dot / (lenA * lenB);
        
        // 确保 cosine 在 [-1, 1] 范围内
        return Math.acos(Math.max(-1, Math.min(1, cosine)));
    }

    /**
     * 计算两个向量之间的有符号角度（弧度）
     * @param a 向量 a
     * @param b 向量 b
     * @param axis 参考轴
     * @returns 有符号角度（弧度）
     */
    public static signedAngle(a: Vector3, b: Vector3, axis: Vector3): number {
        // 计算 a 和 b 的叉积
        const cross = Vector3Util.create();
        Vector3Util.cross(cross, a, b);
        
        // 计算 a 和 b 的点积
        const dot = Vector3Util.dot(a, b);
        
        // 计算 a 和 b 的长度
        const lenA = Vector3Util.len(a);
        const lenB = Vector3Util.len(b);
        
        // 计算角度
        const angle = Math.atan2(Vector3Util.len(cross), dot);
        
        // 确定角度的符号
        const sign = Math.sign(Vector3Util.dot(cross, axis));
        
        return angle * sign;
    }

    /**
     * 常量：上方向 (0, 1, 0)
     */
    public static readonly UP: Vector3 = { x: 0, y: 1, z: 0 };

    /**
     * 常量：下方向 (0, -1, 0)
     */
    public static readonly DOWN: Vector3 = { x: 0, y: -1, z: 0 };

    /**
     * 常量：前方向 (0, 0, 1)
     */
    public static readonly FORWARD: Vector3 = { x: 0, y: 0, z: 1 };

    /**
     * 常量：后方向 (0, 0, -1)
     */
    public static readonly BACK: Vector3 = { x: 0, y: 0, z: -1 };

    /**
     * 常量：右方向 (1, 0, 0)
     */
    public static readonly RIGHT: Vector3 = { x: 1, y: 0, z: 0 };

    /**
     * 常量：左方向 (-1, 0, 0)
     */
    public static readonly LEFT: Vector3 = { x: -1, y: 0, z: 0 };

    /**
     * 常量：零向量 (0, 0, 0)
     */
    public static readonly ZERO: Vector3 = { x: 0, y: 0, z: 0 };

    /**
     * 常量：单位向量 (1, 1, 1)
     */
    public static readonly ONE: Vector3 = { x: 1, y: 1, z: 1 };
}
