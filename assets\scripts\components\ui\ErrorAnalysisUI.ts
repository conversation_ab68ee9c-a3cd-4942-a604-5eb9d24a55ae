import { _decorator, Component, Node, Label, Button, ScrollView, instantiate, Prefab } from 'cc';
import { ErrorManager } from '../../managers/ErrorManager';
import { ErrorPattern } from '../../utils/ErrorAnalyzer';

const { ccclass, property } = _decorator;

/**
 * 错误分析UI组件
 * 用于在游戏中显示错误统计和热点错误信息
 */
@ccclass('ErrorAnalysisUI')
export class ErrorAnalysisUI extends Component {
    @property(Label)
    private titleLabel: Label = null;
    
    @property(Label)
    private errorStatsLabel: Label = null;
    
    @property(ScrollView)
    private errorListScrollView: ScrollView = null;
    
    @property(Button)
    private closeButton: Button = null;
    
    @property(Button)
    private exportButton: Button = null;
    
    @property(Prefab)
    private errorItemPrefab: Prefab = null;
    
    @property(Node)
    private contentNode: Node = null;
    
    @property(Node)
    private analysisPanel: Node = null;
    
    private _errorManager: ErrorManager = null;
    private _isVisible: boolean = false;
    private _updateInterval: number = 5000; // 5秒更新一次
    private _lastUpdateTime: number = 0;
    
    /**
     * 组件初始化
     */
    onLoad() {
        // 默认隐藏面板
        this.hidePanel();
        
        // 注册按钮事件
        if (this.closeButton) {
            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClicked, this);
        }
        
        if (this.exportButton) {
            this.exportButton.node.on(Button.EventType.CLICK, this.onExportButtonClicked, this);
        }
        
        // 获取错误管理器
        this._errorManager = ErrorManager.getInstance();
    }
    
    onDestroy() {
        // 取消按钮事件
        if (this.closeButton) {
            this.closeButton.node.off(Button.EventType.CLICK, this.onCloseButtonClicked, this);
        }
        
        if (this.exportButton) {
            this.exportButton.node.off(Button.EventType.CLICK, this.onExportButtonClicked, this);
        }
    }
    
    update(dt: number) {
        // 如果面板可见，定期更新内容
        if (this._isVisible) {
            const now = Date.now();
            if (now - this._lastUpdateTime > this._updateInterval) {
                this.updateContent();
                this._lastUpdateTime = now;
            }
        }
    }
    
    /**
     * 显示分析面板
     */
    public showPanel() {
        if (this.analysisPanel) {
            this.analysisPanel.active = true;
            this._isVisible = true;
            this.updateContent();
        }
    }
    
    /**
     * 隐藏分析面板
     */
    public hidePanel() {
        if (this.analysisPanel) {
            this.analysisPanel.active = false;
            this._isVisible = false;
        }
    }
    
    /**
     * 更新面板内容
     */
    private updateContent() {
        if (!this._errorManager) return;
        
        // 更新错误统计信息
        this.updateErrorStats();
        
        // 更新错误列表
        this.updateErrorList();
    }
    
    /**
     * 更新错误统计信息
     */
    private updateErrorStats() {
        if (!this.errorStatsLabel) return;
        
        const stats = this._errorManager.getErrorStats();
        const history = this._errorManager.getErrorHistory();
        const pendingErrors = this._errorManager.getPendingErrors().length;
        const recoveredCount = history.filter(item => item.recovered).length;
        
        let statsText = `错误统计信息:\n`;
        statsText += `总错误数: ${stats.totalErrors}\n`;
        statsText += `待处理错误: ${pendingErrors}\n`;
        statsText += `已恢复错误: ${recoveredCount}\n`;
        statsText += `错误率: ${stats.errorRate.toFixed(2)} 个/分钟\n\n`;
        
        statsText += `按类型统计:\n`;
        for (const type in stats.byType) {
            statsText += `${type}: ${stats.byType[type]}\n`;
        }
        
        statsText += `\n按严重程度统计:\n`;
        for (const severity in stats.bySeverity) {
            statsText += `${severity}: ${stats.bySeverity[severity]}\n`;
        }
        
        this.errorStatsLabel.string = statsText;
    }
    
    /**
     * 更新错误列表
     */
    private updateErrorList() {
        if (!this.contentNode || !this.errorItemPrefab) return;
        
        // 清空当前内容
        this.contentNode.removeAllChildren();
        
        // 获取错误热点列表
        const hotspots = this._errorManager.getErrorHotspots();
        
        if (hotspots.length === 0) {
            const emptyLabel = new Node('EmptyLabel');
            const label = emptyLabel.addComponent(Label);
            label.string = '没有发现热点错误';
            this.contentNode.addChild(emptyLabel);
            return;
        }
        
        // 添加每个热点错误
        for (const hotspot of hotspots) {
            const itemNode = instantiate(this.errorItemPrefab);
            
            // 设置错误信息
            const titleLabel = itemNode.getChildByName('TitleLabel')?.getComponent(Label);
            const detailLabel = itemNode.getChildByName('DetailLabel')?.getComponent(Label);
            
            if (titleLabel) {
                titleLabel.string = `错误模式: ${hotspot.pattern} (${hotspot.count}次)`;
            }
            
            if (detailLabel) {
                detailLabel.string = hotspot.errorSignature.substring(0, 100) + 
                    (hotspot.errorSignature.length > 100 ? '...' : '');
            }
            
            this.contentNode.addChild(itemNode);
        }
    }
    
    /**
     * 关闭按钮点击事件
     */
    private onCloseButtonClicked() {
        this.hidePanel();
    }
    
    /**
     * 导出按钮点击事件
     */
    private onExportButtonClicked() {
        const report = this._errorManager.generateErrorReport();
        console.log(report);
        
        // 实际项目中可以将报告保存到文件或发送到服务器
        alert('错误分析报告已导出到控制台');
    }
    
    /**
     * 切换面板显示状态
     */
    public togglePanel() {
        if (this._isVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }
}
