import { DependencyContainer } from '../containers/DependencyContainer';

/**
 * 依赖注入类型
 */
export enum InjectType {
  PROPERTY, // 属性注入
  CONSTRUCTOR, // 构造函数注入
  METHOD // 方法注入
}

/**
 * 依赖注入元数据
 */
interface InjectMetadataEntry {
  serviceId: string;
  type: InjectType;
  optional?: boolean; // 是否为可选依赖
}

/**
 * 用于存储和管理注入元数据的类
 */
class InjectMetadata {
  private static _instance: InjectMetadata;
  private _metadata: Map<any, Map<string, InjectMetadataEntry>> = new Map();
  private _debug: boolean = false; // 是否启用调试日志

  private constructor() { }

  /**
   * 获取单例实例
   */
  public static getInstance(): InjectMetadata {
    if (!this._instance) {
      this._instance = new InjectMetadata();
    }
    return this._instance;
  }

  /**
   * 设置调试模式
   * @param debug 是否启用调试日志
   */
  public setDebug(debug: boolean): void {
    this._debug = debug;
  }

  /**
   * 注册注入元数据
   * @param target 目标类
   * @param propertyKey 属性名
   * @param metadata 元数据
   */
  public register(target: any, propertyKey: string, metadata: InjectMetadataEntry): void {
    if (this._debug) {
      console.log(`[DI] Registering metadata for ${target.constructor.name}.${propertyKey} -> ${metadata.serviceId}`);
    }

    // 确保目标类有元数据映射
    if (!this._metadata.has(target.constructor)) {
      this._metadata.set(target.constructor, new Map());
      if (this._debug) {
        console.log(`[DI] Created new metadata map for ${target.constructor.name}`);
      }
    }

    // 注册属性的元数据
    const targetMetadata = this._metadata.get(target.constructor);
    targetMetadata.set(propertyKey, metadata);

    if (this._debug) {
      console.log(`[DI] Metadata registered: ${target.constructor.name}.${propertyKey} -> ${metadata.serviceId}`);
    }
  }

  /**
   * 获取注入元数据
   * @param target 目标类
   * @param propertyKey 属性名
   * @returns 元数据
   */
  public getMetadata(target: any, propertyKey: string): InjectMetadataEntry | undefined {
    const targetMetadata = this._metadata.get(target.constructor);
    if (!targetMetadata) return undefined;
    return targetMetadata.get(propertyKey);
  }

  /**
   * 获取类的所有注入元数据
   * @param target 目标类
   * @returns 属性名到元数据的映射
   */
  public getClassMetadata(target: any): Map<string, InjectMetadataEntry> | undefined {
    // 尝试获取目标类的元数据
    let metadata = this._metadata.get(target.constructor);

    // 如果没有找到，尝试获取目标类的原型链上的元数据
    if (!metadata) {
      // 遍历所有注册的类
      for (const [constructor, meta] of this._metadata.entries()) {
        // 检查目标是否是该类的实例
        if (target instanceof constructor) {
          if (this._debug) {
            console.log(`[DI] Found metadata for ${constructor.name} (${target.constructor.name} is an instance of it)`);
          }
          metadata = meta;
          break;
        }
      }
    }

    return metadata;
  }

  /**
   * 清除所有元数据
   */
  public clear(): void {
    this._metadata.clear();
  }
}

/**
 * 服务注入装饰器
 * 用于自动注入依赖服务
 *
 * 使用示例：
 * ```
 * class MyClass {
 *   @Inject('gameManager')
 *   private _gameManager: IGameManager;
 *
 *   public doSomething(): void {
 *     // 直接使用注入的服务
 *     this._gameManager.addTower(...);
 *   }
 * }
 * ```
 *
 * @param serviceId 服务标识符
 * @param options 注入选项
 */
export function Inject(serviceId: string, options: { optional?: boolean } = {}) {
  console.log(`[DI] Inject service '${serviceId}`);
  return function (target: any, propertyKey: string, descriptor?: PropertyDescriptor): void {
    // 注册元数据
    InjectMetadata.getInstance().register(target, propertyKey, {
      serviceId,
      type: InjectType.PROPERTY,
      optional: options.optional
    });
    console.log(`[DI] register service '${serviceId}' for property '${propertyKey}'`, target);
    // 定义属性的 getter
    const getter = function (this: any) {
      // 检查是否已经有缓存的服务实例
      const privateKey = `__${propertyKey}_service`;

      if (!this[privateKey]) {
        try {
          // 从 DependencyContainer 获取服务
          const container = DependencyContainer.getInstance();
          container.initialize();
          if (options.optional) {
            // 对于可选依赖，使用 tryResolve
            this[privateKey] = container.tryResolve(serviceId);
          } else {
            // 对于必需依赖，使用 resolve
            this[privateKey] = container.resolve(serviceId);
          }
          console.log(`[DI] Getting service '${serviceId}' for property '${propertyKey}'`, this[privateKey]);
        } catch (error) {
          console.error(`[DI] Failed to inject service '${serviceId}' into property '${propertyKey}':`, error);
          // 如果是可选依赖，设置为 null
          if (options.optional) {
            this[privateKey] = null;
          } else {
            // 重新抛出错误，让调用者处理
            throw error;
          }
        }
      }
      return this[privateKey];
    };
    if ('initializer' in descriptor && !target["_" + propertyKey] && (descriptor as any).initializer) {
      target["_" + propertyKey] = (descriptor as any).initializer();
    }

    delete (descriptor as any).initializer;
    delete descriptor.writable;//

    // 定义属性
    Object.assign(descriptor, {
      get: getter,
      enumerable: true,
      configurable: true
    });


  };
}

/**
 * 可选服务注入装饰器
 * 如果服务不存在，不会抛出错误，而是注入 null
 *
 * @param serviceId 服务标识符
 */
export function InjectOptional(serviceId: string) {
  return Inject(serviceId, { optional: true });
}

/**
 * 自动注入类的所有依赖
 * 在类的构造函数中调用
 * 
 * 优点 提前验证依赖  批量注入依赖 集中的错误处理
 *
 * @param instance 类实例
 * @param options 注入选项
 */
export function injectDependencies(instance: any, options: { debug?: boolean } = {}): void {
  const debug = options.debug || false;

  if (debug) {
    console.log(`[DI] Injecting dependencies for ${instance.constructor.name}`);
  }

  const metadata = InjectMetadata.getInstance().getClassMetadata(instance);
  console.log('metadata', metadata)
  if (!metadata) {
    if (debug) {
      console.warn(`[DI] No metadata found for ${instance.constructor.name}`);
    }
    return;
  }

  if (debug) {
    console.log(`[DI] Found metadata for ${instance.constructor.name}:`, Array.from(metadata.entries()));
  }

  // 遍历所有注入元数据
  metadata.forEach((meta, propertyKey) => {
    try {
      if (debug) {
        console.log(`[DI] Injecting ${meta.serviceId} into ${instance.constructor.name}.${propertyKey}`);
      }

      const privateKey = `__${propertyKey}_service`;
      if (instance[privateKey] === undefined) {
        try {
          // 从 DependencyContainer 获取服务
          const container = DependencyContainer.getInstance();

          if (meta.optional) {
            // 对于可选依赖，使用 tryResolve
            instance[privateKey] = container.tryResolve(meta.serviceId);
            if (debug) {
              if (instance[privateKey]) {
                console.log(`[DI] Successfully injected optional service ${meta.serviceId}`);
              } else {
                console.log(`[DI] Optional service ${meta.serviceId} not found, setting to null`);
              }
            }
          } else {
            // 对于必需依赖，使用 resolve
            instance[privateKey] = container.resolve(meta.serviceId);
            if (debug) {
              console.log(`[DI] Successfully injected service ${meta.serviceId}`);
            }
          }
        } catch (error) {
          console.error(`[DI] Failed to inject service '${meta.serviceId}' into property '${propertyKey}':`, error);
          if (meta.optional) {
            instance[privateKey] = null;
          } else {
            // 对于必需依赖，重新抛出错误
            throw error;
          }
        }
      } else if (debug) {
        console.log(`[DI] Service ${meta.serviceId} already injected`);
      }
    } catch (error) {
      console.error(`[DI] Failed to inject service '${meta.serviceId}' into property '${propertyKey}':`, error);
    }
  });
}

/**
 * 启用依赖注入调试模式
 * @param enable 是否启用
 */
export function enableDIDebug(enable: boolean = true): void {
  InjectMetadata.getInstance().setDebug(enable);
}