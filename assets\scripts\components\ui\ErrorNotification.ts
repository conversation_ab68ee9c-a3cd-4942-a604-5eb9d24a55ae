import { _decorator, Component, Node, Label, Button, UIOpacity, tween, Vec3 } from 'cc';
import { ErrorManager, ErrorSeverity } from '../../managers/ErrorManager';
import { GameEvent, GameEventMap } from '../../events/GameEvents';
import { EventManager } from '../../managers/EventManager';
import { Inject } from '../../decorators/Inject';

const { ccclass, property } = _decorator;

/**
 * 错误通知UI组件
 * 用于在游戏中显示错误通知
 */
@ccclass('ErrorNotification')
export class ErrorNotification extends Component {
    // 引用错误信息标签
    @property(Label)
    private errorMessageLabel: Label = null;
    
    // 引用错误上下文标签
    @property(Label)
    private errorContextLabel: Label = null;
    
    // 引用确认按钮
    @property(Button)
    private confirmButton: Button = null;
    
    // 引用UI不透明度组件
    @property(UIOpacity)
    private uiOpacity: UIOpacity = null;
    
    // 错误管理器
    private _errorManager: ErrorManager = ErrorManager.getInstance();
    
    // 事件管理器
    @Inject('eventManager')
    private _eventManager: EventManager<GameEventMap>;
    
    // 当前显示的错误ID
    private _currentErrorId: string = '';
    
    // 生命周期：组件加载
    onLoad() {
        // 初始化UI
        this.initializeUI();
        
        // 订阅错误事件
        if (this._eventManager) {
            this._eventManager.on(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        } else {
            console.error('ErrorNotification: 事件管理器未注入');
            // 尝试手动获取事件管理器
            this._eventManager = EventManager.getInstance();
            if (this._eventManager) {
                this._eventManager.on(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
            }
        }
    }
    
    // 生命周期：组件销毁
    onDestroy() {
        // 取消订阅错误事件
        if (this._eventManager) {
            this._eventManager.off(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        }
        
        // 取消按钮事件
        if (this.confirmButton) {
            this.confirmButton.node.off(Button.EventType.CLICK, this.onConfirmButtonClicked, this);
        }
    }
    
    // 初始化UI
    private initializeUI() {
        // 确保UI组件最初是隐藏的
        if (this.node) {
            this.node.active = false;
        }
        
        if (this.confirmButton) {
            // 注册确认按钮事件
            this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmButtonClicked, this);
        }
    }
    
    // 错误发生事件处理
    private onErrorOccurred(data: { error: Error, context: string, isFatal: boolean, timestamp: number, diagnosis?: any }) {
        if (!data || !data.error) {
            console.error('ErrorNotification: 接收到无效的错误事件数据');
            return;
        }
        
        // 保存错误ID以便后续处理
        this._currentErrorId = `error-${data.timestamp || Date.now()}`;
        
        // 判断错误严重程度
        const severity = data.isFatal ? ErrorSeverity.FATAL : ErrorSeverity.ERROR;
        
        // 根据严重程度和诊断信息决定是否显示UI
        if (severity >= ErrorSeverity.ERROR || (data.diagnosis && data.diagnosis.priority >= 7)) {
            this.showErrorNotification(data.error, data.context, severity, data.diagnosis);
        } else {
            // 对于轻微错误，可以只在控制台记录
            console.warn(`轻微错误 [${data.context}]: ${data.error.message}`);
        }
    }
    
    // 显示错误通知
    public showErrorNotification(error: Error, context: string, severity: ErrorSeverity, diagnosis?: any) {
        if (!error) {
            console.error('ErrorNotification: 尝试显示空错误');
            return;
        }
        
        // 设置错误信息
        if (this.errorMessageLabel) {
            this.errorMessageLabel.string = error.message || '未知错误';
            
            // 如果有诊断建议，添加到错误信息中
            if (diagnosis && diagnosis.suggestedFix) {
                this.errorMessageLabel.string += `\n建议: ${diagnosis.suggestedFix}`;
            }
        }
        
        // 设置错误上下文
        if (this.errorContextLabel) {
            const severityText = ErrorSeverity[severity] || 'UNKNOWN';
            this.errorContextLabel.string = `[${severityText}] ${context || '未知上下文'}`;
        }
        
        // 显示通知
        if (this.node) {
            this.node.active = true;
        }
        
        // 初始化透明度为0
        if (this.uiOpacity) {
            this.uiOpacity.opacity = 0;
            // 淡入动画
            tween(this.uiOpacity)
                .to(0.3, { opacity: 255 })
                .start();
        }
        
        // 播放UI显示动画
        if (this.node) {
            const originalScale = this.node.scale.clone();
            this.node.scale = new Vec3(originalScale.x * 0.5, originalScale.y * 0.5, originalScale.z);
            
            tween(this.node)
                .to(0.3, { scale: originalScale })
                .start();
        }
    }
    
    // 确认按钮点击事件
    private onConfirmButtonClicked() {
        // 隐藏通知
        this.hideNotification();
        
        // 如果有存储错误ID，标记为已处理
        if (this._currentErrorId && this._errorManager) {
            this._errorManager.markErrorAsRecovered(this._currentErrorId, 'UserAcknowledged');
            this._currentErrorId = '';
        }
    }
    
    // 隐藏通知
    private hideNotification() {
        // 淡出动画
        if (this.uiOpacity && this.node) {
            tween(this.uiOpacity)
                .to(0.3, { opacity: 0 })
                .call(() => {
                    if (this.node) {
                        this.node.active = false;
                    }
                })
                .start();
        } else if (this.node) {
            this.node.active = false;
        }
    }
}
