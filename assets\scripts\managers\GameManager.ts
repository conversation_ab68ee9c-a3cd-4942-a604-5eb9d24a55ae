import { EnemyBase } from '../entities/enemies/EnemyBase';
import { ProjectileCore } from '../entities/projectiles/ProjectileCore';
import { WeaponsBase } from '../entities/weapons/WeaponsBase';
import { GameEvent, GameEventMap } from '../events/GameEvents';
import { IGameManager, IProjectileManager } from '../interfaces/IManager';
import { List } from '../utils/List';
import { QuadTree, Bounds } from '../utils/QuadTree';
import { BattleManager } from './BattleManager';
import { EventManager } from './EventManager';

// 简单对象池实现
class EnemyPool {
    private _pool: EnemyBase[] = [];
    get(): EnemyBase | null {
        return this._pool.pop() || null;
    }
    put(enemy: EnemyBase) {
        // 可重置敌人状态
        enemy.reset && enemy.reset();
        this._pool.push(enemy);
    }
}

export class GameManager implements IGameManager {
    private _battleManager: BattleManager;    constructor(
        private readonly _projectileManager: IProjectileManager,
        private readonly _eventManager?: EventManager<GameEventMap>
    ) {
        // 初始化战斗管理器
        this._battleManager = new BattleManager();
        this._battleManager.init(_eventManager);
        
        // 将当前GameManager实例注入BattleManager
        this._battleManager.setGameManager(this);
        
        // 将ProjectileManager注入BattleManager，用于数学模式下的子弹碰撞检测
        this._battleManager.setProjectileManager(this._projectileManager);
    }

    /**
     * 添加投射物
     * @param projectile 要添加的投射物
     * @param type 投射物类型，用于对象池管理
     */
    addProjectile(projectile: ProjectileCore, type: string = 'default') {
        // 直接添加到投射物管理器
        this._projectileManager.addProjectile(projectile);

        // 记录类型，用于后续回收到对象池
        if (type) {
            this._projectileManager.addToPool(type, projectile);
        }
    }

    /**
     * 创建或获取投射物
     * 优先从对象池获取，如果对象池中没有可用对象，则使用工厂函数创建新的投射物
     * @param type 投射物类型标识符
     * @param factory 创建投射物的工厂函数，当对象池中没有可用对象时调用
     * @returns 投射物实例
     */
    createProjectile(type: string, factory: () => ProjectileCore): ProjectileCore {
        return this._projectileManager.createOrGetProjectile(type, factory);
    }

    /**
     * 移除投射物
     * @param projectile 要移除的投射物
     */
    removeProjectile(projectile: ProjectileCore): void {
        console.log(`GameManager: 回收投射物 ${projectile.constructor.name}`);
        this._projectileManager.recycleProjectile(projectile);
    }

    /**
     * 添加武器
     * @param weapon 武器实例
     */
    addWeapon(weapon: WeaponsBase): void {
        this._battleManager.addWeapon(weapon);
        console.log(`GameManager: 武器已添加到战斗管理器，类型: ${weapon.constructor.name}`);
    }

    /**
     * 添加敌人
     * @param createEnemyFn 创建敌人的工厂函数
     * @returns 敌人实例
     */
    addEnemy(createEnemyFn: () => EnemyBase): EnemyBase {
        const enemy = this._battleManager.addEnemy(createEnemyFn);
        console.log(`GameManager: 敌人已添加到战斗管理器，ID: ${enemy.id}`);
        return enemy;
    }

    /**
     * 移除敌人
     * @param enemy 要移除的敌人
     */
    removeEnemy(enemy: EnemyBase): void {
        this._battleManager.removeEnemy(enemy);
    }

    /**
     * 获取所有武器
     * @returns 武器数组
     */
    getCharacters(): WeaponsBase[] {
        return this._battleManager.getWeapons();
    }

    /**
     * 更新游戏状态
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        // 更新投射物
        this._projectileManager.update(deltaTime);

        // 更新战斗状态
        this._battleManager.update(deltaTime);
    }

    /**
     * 获取战斗管理器
     */
    public get battleManager(): BattleManager {
        return this._battleManager;
    }
}