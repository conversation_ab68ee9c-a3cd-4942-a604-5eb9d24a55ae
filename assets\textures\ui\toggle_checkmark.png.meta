{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5328a4ef-7df2-4b88-b558-b5e388b13af3", "files": [".json", ".png"], "subMetas": {"6c48a": {"ver": "1.0.22", "importer": "texture", "uuid": "5328a4ef-7df2-4b88-b558-b5e388b13af3@6c48a", "imported": true, "files": [".json"], "subMetas": {}, "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "premultiplyAlpha": false, "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "5328a4ef-7df2-4b88-b558-b5e388b13af3", "visible": false}, "displayName": "toggle_checkmark", "id": "6c48a", "name": "texture"}, "f9941": {"ver": "1.0.12", "importer": "sprite-frame", "uuid": "5328a4ef-7df2-4b88-b558-b5e388b13af3@f9941", "imported": true, "files": [".json"], "subMetas": {}, "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "premultiplyAlpha": false, "generateMipmap": false, "anisotropy": 1, "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 4, "trimY": 5, "width": 20, "height": 18, "rawWidth": 28, "rawHeight": 28, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "isUuid": true, "imageUuidOrDatabaseUri": "5328a4ef-7df2-4b88-b558-b5e388b13af3@6c48a", "atlasUuid": "", "mipfilter": "none", "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-10, -9, 0, 10, -9, 0, -10, 9, 0, 10, 9, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [4, 23, 24, 23, 4, 5, 24, 5], "nuv": [0.14285714285714285, 0.17857142857142858, 0.8571428571428571, 0.17857142857142858, 0.14285714285714285, 0.8214285714285714, 0.8571428571428571, 0.8214285714285714], "minPos": [-10, -9, 0], "maxPos": [10, 9, 0]}}, "displayName": "toggle_checkmark", "id": "f9941", "name": "spriteFrame"}}, "userData": {"type": "sprite-frame", "redirect": "5328a4ef-7df2-4b88-b558-b5e388b13af3@6c48a", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false}}