/**
 * 四叉树实现，用于高效地查询空间中的对象
 */

// 定义矩形边界
export interface Bounds {
    x: number;      // 矩形中心点 x 坐标
    z: number;      // 矩形中心点 z 坐标
    width: number;  // 矩形宽度
    height: number; // 矩形高度
}

// 定义可放入四叉树的对象接口
export interface QuadTreeObject {
    position: { x: number, y: number, z: number }; // 对象位置
    getBounds?(): Bounds; // 可选方法，返回对象的边界
}

/**
 * 四叉树节点
 */
export class QuadTree<T extends QuadTreeObject> {
    private readonly MAX_OBJECTS: number = 10; // 每个节点最多容纳的对象数量
    private readonly MAX_LEVELS: number = 5;   // 最大深度
    
    private level: number;           // 当前节点深度
    private objects: T[] = [];       // 当前节点包含的对象
    private bounds: Bounds;          // 当前节点的边界
    private nodes: QuadTree<T>[] = []; // 子节点
    
    /**
     * 创建四叉树节点
     * @param bounds 边界
     * @param level 深度（默认为0，即根节点）
     */
    constructor(bounds: Bounds, level: number = 0) {
        this.bounds = bounds;
        this.level = level;
    }
    
    /**
     * 清空四叉树
     */
    public clear(): void {
        this.objects = [];
        
        // 递归清空子节点
        for (let i = 0; i < this.nodes.length; i++) {
            if (this.nodes[i]) {
                this.nodes[i].clear();
            }
        }
        
        this.nodes = [];
    }
    
    /**
     * 分割当前节点为四个子节点
     */
    private split(): void {
        const subWidth = this.bounds.width / 2;
        const subHeight = this.bounds.height / 2;
        const x = this.bounds.x;
        const z = this.bounds.z;
        
        // 创建四个子节点（顺序：右上、右下、左下、左上）
        this.nodes[0] = new QuadTree<T>({
            x: x + subWidth / 2,
            z: z + subHeight / 2,
            width: subWidth,
            height: subHeight
        }, this.level + 1);
        
        this.nodes[1] = new QuadTree<T>({
            x: x + subWidth / 2,
            z: z - subHeight / 2,
            width: subWidth,
            height: subHeight
        }, this.level + 1);
        
        this.nodes[2] = new QuadTree<T>({
            x: x - subWidth / 2,
            z: z - subHeight / 2,
            width: subWidth,
            height: subHeight
        }, this.level + 1);
        
        this.nodes[3] = new QuadTree<T>({
            x: x - subWidth / 2,
            z: z + subHeight / 2,
            width: subWidth,
            height: subHeight
        }, this.level + 1);
    }
    
    /**
     * 确定对象应该放入哪个子节点
     * @param obj 要放入的对象
     * @returns 子节点索引数组，可能属于多个子节点
     */
    private getIndex(obj: T): number[] {
        const indexes: number[] = [];
        const pos = obj.position;
        
        // 获取对象的中心点
        const objX = pos.x;
        const objZ = pos.z;
        
        // 计算垂直和水平中点
        const verticalMidpoint = this.bounds.x;
        const horizontalMidpoint = this.bounds.z;
        
        // 对象是否在上半部分
        const topQuadrant = objZ > horizontalMidpoint;
        // 对象是否在下半部分
        const bottomQuadrant = objZ <= horizontalMidpoint;
        
        // 对象在右半部分
        if (objX > verticalMidpoint) {
            if (topQuadrant) {
                indexes.push(0); // 右上
            }
            if (bottomQuadrant) {
                indexes.push(1); // 右下
            }
        }
        // 对象在左半部分
        else if (objX <= verticalMidpoint) {
            if (topQuadrant) {
                indexes.push(3); // 左上
            }
            if (bottomQuadrant) {
                indexes.push(2); // 左下
            }
        }
        
        return indexes;
    }
    
    /**
     * 插入对象到四叉树
     * @param obj 要插入的对象
     */
    public insert(obj: T): void {
        // 如果已经有子节点
        if (this.nodes.length) {
            const indexes = this.getIndex(obj);
            
            // 将对象添加到所有相关的子节点
            for (let i = 0; i < indexes.length; i++) {
                this.nodes[indexes[i]].insert(obj);
            }
            
            return;
        }
        
        // 将对象添加到当前节点
        this.objects.push(obj);
        
        // 检查是否需要分割
        if (this.objects.length > this.MAX_OBJECTS && this.level < this.MAX_LEVELS) {
            // 如果还没有子节点，则分割
            if (!this.nodes.length) {
                this.split();
            }
            
            // 将当前节点的对象重新分配到子节点
            for (let i = 0; i < this.objects.length; i++) {
                const indexes = this.getIndex(this.objects[i]);
                for (let j = 0; j < indexes.length; j++) {
                    this.nodes[indexes[j]].insert(this.objects[i]);
                }
            }
            
            // 清空当前节点的对象
            this.objects = [];
        }
    }
    
    /**
     * 检查矩形是否与另一个矩形相交
     * @param r1 第一个矩形
     * @param r2 第二个矩形
     * @returns 是否相交
     */
    private static intersects(r1: Bounds, r2: Bounds): boolean {
        return !(r2.x - r2.width/2 > r1.x + r1.width/2 ||
                r2.x + r2.width/2 < r1.x - r1.width/2 ||
                r2.z - r2.height/2 > r1.z + r1.height/2 ||
                r2.z + r2.height/2 < r1.z - r1.height/2);
    }
    
    /**
     * 检查点是否在矩形内
     * @param point 点坐标
     * @param bounds 矩形边界
     * @returns 是否在矩形内
     */
    private static pointInBounds(point: {x: number, z: number}, bounds: Bounds): boolean {
        return point.x >= bounds.x - bounds.width/2 &&
               point.x <= bounds.x + bounds.width/2 &&
               point.z >= bounds.z - bounds.height/2 &&
               point.z <= bounds.z + bounds.height/2;
    }
    
    /**
     * 查询指定范围内的所有对象
     * @param range 查询范围
     * @returns 范围内的对象数组
     */
    public query(range: Bounds): T[] {
        const result: T[] = [];
        
        // 如果查询范围与当前节点不相交，直接返回空数组
        if (!QuadTree.intersects(this.bounds, range)) {
            return result;
        }
        
        // 检查当前节点中的对象
        for (let i = 0; i < this.objects.length; i++) {
            const obj = this.objects[i];
            const pos = obj.position;
            
            // 如果对象在查询范围内，添加到结果中
            if (QuadTree.pointInBounds({x: pos.x, z: pos.z}, range)) {
                result.push(obj);
            }
        }
        
        // 如果没有子节点，直接返回当前结果
        if (!this.nodes.length) {
            return result;
        }
        
        // 递归查询子节点
        for (let i = 0; i < this.nodes.length; i++) {
            const subResults = this.nodes[i].query(range);
            result.push(...subResults);
        }
        
        return result;
    }
    
    /**
     * 查询指定圆形范围内的所有对象
     * @param center 圆心
     * @param radius 半径
     * @returns 范围内的对象数组
     */
    public queryCircle(center: {x: number, z: number}, radius: number): T[] {
        // 首先使用包围盒查询
        const range: Bounds = {
            x: center.x,
            z: center.z,
            width: radius * 2,
            height: radius * 2
        };
        
        // 获取矩形范围内的所有对象
        const candidateObjects = this.query(range);
        
        // 过滤出在圆形范围内的对象
        return candidateObjects.filter(obj => {
            const dx = obj.position.x - center.x;
            const dz = obj.position.z - center.z;
            const distanceSquared = dx * dx + dz * dz;
            return distanceSquared <= radius * radius;
        });
    }
}
