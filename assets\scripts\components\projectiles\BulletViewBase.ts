import { _decorator, Node, Vec3, Quat, Component, geometry, PhysicsSystem, Collider, RigidBody } from 'cc';
import { ProjectileCore, ProjectileMode } from '../../entities/projectiles/ProjectileCore';




const { ccclass, property } = _decorator;

/**
 * 子弹视图类
 * 负责子弹的视觉表现
 */
@ccclass('BulletViewBase')
export abstract class BulletViewBase extends Component {


    /**
 * 最大生命周期（秒）
 */
    @property({ displayName: '最大生命周期（秒）' })
    protected maxLifetime: number = 10;


    /**
    * 碰撞器
    */
    protected _collider: Collider = null;

    /**
     * 刚体
     */
    protected _rigidBody: RigidBody = null;

    /**
      * 初始位置
      */
    protected initialPosition: Vec3 = new Vec3();

    /**
     * 目标位置
     */
    protected targetPosition: Vec3 = new Vec3();

    /**
     * 初始速度
     */
    protected initialVelocity: number = 0;

    /**
     * 初始角度
     */
    protected initialAngle: number = 0;

    /**
     * 发射时间
     */
    protected fireTime: number = 0;

    /**
     * 飞行时间
     */
    protected flightTime: number = 0;

    /**
     * 时间缩放
     */
    protected timeScale: number = 1.0;


    // 用于旋转计算
    private tempRotation: Quat = new Quat();
    protected lastPosition: Vec3 = new Vec3(); // 上一帧位置，用于射线检测和旋转计算
    protected curPosition: Vec3 = new Vec3();  // 当前位置
    private currentVelocity: Vec3 = new Vec3();

    // 是否启用旋转
    protected enableRotation: boolean = true;


    /**
     * 前进方向（数学模式使用）
     */
    protected forwardDirection: Vec3 = new Vec3();


    // 最近一次碰撞信息（仍然需要Vec3对象用于传递给其他API）
    protected lastHitPoint: Vec3 = new Vec3(); // 碰撞点
    protected lastHitNormal: Vec3 = new Vec3(); // 碰撞法线

    // 临时Vec3对象，用于避免在方法中创建新的Vec3
    private tempVec3_1: Vec3 = new Vec3();
    private tempVec3_2: Vec3 = new Vec3();


    protected sphereRadius: number = 1.0; // 球体检测的半径
    protected rayLength: number = 1.0; // 射线检测的长度



    /**
     * 是否已被触发
     */
    protected triggered: boolean = false;



    // 逻辑对象（武器塔实例）
    protected _logic: ProjectileCore | null = null;

    protected onLoad(): void {
        // 获取组件
        this._collider = this.getComponent(Collider);
        this._rigidBody = this.getComponent(RigidBody);
    }

    get collider(): Collider | null {
        return this._collider;
    }
    get rigidBody(): RigidBody | null {
        return this._rigidBody;
    }
    // 设置逻辑对象
    public setLogic(logic: ProjectileCore) {
        this._logic = logic;
        this.node.setWorldPosition(this._logic.firePosition);
    }
    /**
     * 清理资源
     * 在投射物被销毁时调用
     */
    public cleanup(): void {
        this.node.active = false;
        // 子类可以重写此方法以清理特定资源

        //console.warn('cleanup:',this.node.active)
    }

    /**
     * 重置视图状态
     * 在投射物被对象池回收时调用
     */
    public reset(): void {


        // 重置节点状态
        this.node.active = true;
        this.node.setWorldPosition(this._logic.initialPosition);
        // 子类可以重写此方法以重置特定状态
    }

    // 更新视图
    public updateView(deltaTime: number): void {
        if (!this._logic || !this._logic.isActive) {
            this.node.active = false;
            return
        };
        if (!this.node.active) {
            this.node.active = true;
        }
        this.updatePosition(deltaTime);


    }
    /**
        * 更新数学模式下的位置
        * @param dt 帧间隔时间（秒）
        */
    protected updatePosition(dt: number) {
       
        if (!this._logic || !this._logic.isActive) return
        if (this._logic.mode == ProjectileMode.MATH) {
            // 保存上一帧位置，用于射线检测
            this.node.setWorldPosition(this._logic.position);
            // 更新旋转
            this.node.setWorldRotation(this._logic.rotation);
        } else {
            // 更新旋转
            this.node.setWorldRotation(this._logic.rotation);
        }


    }

}