
import { IProjectileManager } from '../interfaces/IManager';
import { ProjectileCore } from '../entities/projectiles/ProjectileCore';

/**
 * 投射物管理器命令类型
 */
type ProjectileCommand = {
    type: 'add' | 'remove' | 'recycle';
    projectile: ProjectileCore;
};

/**
 * 投射物对象池
 * 管理特定类型投射物的回收和复用
 */
class ProjectilePool {
    /** 对象池中的可用对象 */
    private _pool: Map<string, ProjectileCore[]> = new Map();

    /** 对象池大小限制，防止无限增长 */
    private readonly _maxPoolSize: number = 50;

    /**
     * 从对象池获取投射物
     * @param type 投射物类型标识符
     * @returns 投射物实例，如果对象池中没有可用对象则返回null
     */
    public get(type: string): ProjectileCore | null {
        // 检查对象池中是否有可用对象
        const pooledObjects = this._pool.get(type) || [];

        if (pooledObjects.length > 0) {
            // 从对象池中取出一个对象
            const projectile = pooledObjects.pop();
            console.log(`[ProjectilePool] 从对象池获取投射物: ${type}, 剩余: ${pooledObjects.length}`);

            return projectile;
        }

        // 对象池中没有可用对象
        return null;
    }

    /**
     * 回收投射物到对象池
     * @param type 投射物类型标识符
     * @param projectile 要回收的投射物
     */
    public recycle(type: string, projectile: ProjectileCore): void {
        // 确保对象池存在
        if (!this._pool.has(type)) {
            this._pool.set(type, []);
        }

        const pooledObjects = this._pool.get(type);

        // 检查对象池大小是否超过限制
        if (pooledObjects.length >= this._maxPoolSize) {
            console.log(`[ProjectilePool] 对象池已满，销毁投射物: ${type}`);
            projectile.destroy();
            return;
        }

        // 将投射物添加到对象池
        pooledObjects.push(projectile);
        console.log(`[ProjectilePool] 回收投射物: ${type}, 当前数量: ${pooledObjects.length}`);
    }

    /**
     * 清空对象池
     */
    public clear(): void {
        // 销毁所有对象池中的对象
        for (const [type, pooledObjects] of this._pool.entries()) {
            console.log(`[ProjectilePool] 清空对象池: ${type}, 数量: ${pooledObjects.length}`);

            for (const projectile of pooledObjects) {
                projectile.destroy();
            }
        }

        // 清空对象池
        this._pool.clear();
    }

    /**
     * 获取对象池统计信息
     * @returns 对象池统计信息
     */
    public getStats(): { type: string, count: number }[] {
        const stats: { type: string, count: number }[] = [];

        for (const [type, pooledObjects] of this._pool.entries()) {
            stats.push({ type, count: pooledObjects.length });
        }

        return stats;
    }
}

/**
 * 投射物管理器
 * 使用命令队列模式处理投射物的添加和删除，避免在迭代过程中修改集合
 */
export class ProjectileManager implements IProjectileManager {
    /** 投射物列表 */
    private _projectiles: ProjectileCore[] = [];

    /** 待处理的命令队列 */
    private _pendingCommands: ProjectileCommand[] = [];

    /** 帧计数器，用于定期清理 */
    private _frameCount: number = 0;

    /** 清理频率（每隔多少帧清理一次） */
    private readonly CLEANUP_INTERVAL: number = 30;

    /** 投射物对象池 */
    private _projectilePool: ProjectilePool;

    /** 投射物类型映射，用于回收时确定类型 */
    private _projectileTypes: Map<ProjectileCore, string> = new Map();

    constructor() {
        this._projectilePool = new ProjectilePool();
    }

    /**
     * 将投射物添加到对象池
     * @param type 投射物类型标识符
     * @param projectile 要添加到对象池的投射物
     */
    public addToPool(type: string, projectile: ProjectileCore): void {
        // 记录投射物类型，用于回收时确定类型
        this._projectileTypes.set(projectile, type);

        // 回收到对象池
        this._projectilePool.recycle(type, projectile);
    }

    /**
     * 从对象池获取投射物
     * @param type 投射物类型标识符
     * @returns 投射物实例，如果对象池中没有可用对象则返回null
     */
    public getFromPool(type: string): ProjectileCore | null {
        const projectile = this._projectilePool.get(type);

        if (projectile) {
            // 记录投射物类型，用于回收时确定类型
            this._projectileTypes.set(projectile, type);

            // 重置状态机状态，确保可以正确切换到活跃状态
            if (projectile.stateName === 'destroyed') {
                console.log(`[ProjectileManager] 从对象池获取的投射物处于销毁状态，重置状态机`);
                (projectile as any)._stateMachine.resetToInitialState();
            }

            // 添加到活跃投射物列表
            this.addProjectile(projectile);
        }

        return projectile;
    }

    /**
     * 创建或获取投射物
     * 优先从对象池获取，如果对象池中没有可用对象，则使用工厂函数创建新的投射物
     * @param type 投射物类型标识符
     * @param factory 创建投射物的工厂函数，当对象池中没有可用对象时调用
     * @returns 投射物实例
     */
    public createOrGetProjectile(type: string, factory: () => ProjectileCore): ProjectileCore {
        // 尝试从对象池获取
        let projectile = this.getFromPool(type);

        // 如果对象池中没有可用对象，则创建新的投射物
        if (!projectile) {
            projectile = factory();

            // 记录投射物类型，用于回收时确定类型
            this._projectileTypes.set(projectile, type);

            // 添加到活跃投射物列表
            this.addProjectile(projectile);

            console.log(`[ProjectileManager] 对象池中没有可用的 ${type} 投射物，创建新实例`);
        } else {
            console.log(`[ProjectileManager] 从对象池获取 ${type} 投射物`);
        }

        return projectile;
    }

    /**
     * 添加投射物
     * 将添加命令放入队列，在下一帧统一处理
     * @param projectile 要添加的投射物
     */
    public addProjectile(projectile: ProjectileCore): void {
        this._pendingCommands.push({ type: 'add', projectile });
    }

    /**
     * 移除投射物
     * 将移除命令放入队列，在下一帧统一处理
     * @param projectile 要移除的投射物
     */
    public removeProjectile(projectile: ProjectileCore): void {
        // 立即标记为不活跃，但延迟从数组中移除
        if (projectile) {
            projectile.destroy();
            this._pendingCommands.push({ type: 'remove', projectile });
        }
    }

    /**
     * 回收投射物到对象池
     * @param projectile 要回收的投射物
     */
    public recycleProjectile(projectile: ProjectileCore): void {
        if (!projectile) return;

        // 获取投射物类型
        const type = this._projectileTypes.get(projectile);
        if (!type) {
            console.warn('[ProjectileManager] 无法回收未知类型的投射物');
            this.removeProjectile(projectile);
            return;
        }

        // 标记为不活跃，传入 skipRecycle=true 避免循环调用
        projectile.destroy(true);

        // 重置状态机状态为空闲状态，以便下次使用时可以正确切换到活跃状态
        if (projectile.stateName === 'destroyed') {
            console.log(`[ProjectileManager] 重置投射物状态机状态为空闲状态`);
            // 使用内部方法重置状态机
            (projectile as any)._stateMachine.resetToInitialState();
        }

        // 将回收命令放入队列
        this._pendingCommands.push({ type: 'recycle', projectile });
    }

    /**
     * 处理待处理的命令
     * 在每帧开始时调用，处理所有待处理的添加和移除命令
     */
    private processPendingCommands(): void {
        if (this._pendingCommands.length === 0) return;

        // 处理所有待处理的命令
        for (const command of this._pendingCommands) {
            switch (command.type) {
                case 'add':
                    // 确保不重复添加同一个投射物
                    const index = this._projectiles.indexOf(command.projectile);
                    if (index === -1) {
                        this._projectiles.push(command.projectile);
                    }
                    break;

                case 'recycle':
                    // 从活跃列表中移除
                    const recycleIndex = this._projectiles.indexOf(command.projectile);
                    if (recycleIndex !== -1) {
                        this._projectiles.splice(recycleIndex, 1);
                    }

                    // 回收到对象池
                    const type = this._projectileTypes.get(command.projectile);
                    if (type) {
                        this._projectilePool.recycle(type, command.projectile);
                        this._projectileTypes.delete(command.projectile);
                    }
                    break;

                // 注意：'remove' 类型的命令不在这里处理，而是在清理阶段统一处理
            }
        }

        // 清空命令队列
        this._pendingCommands = [];
    }

    /**
     * 清理不活跃的投射物
     * 定期调用，从数组中移除所有标记为不活跃的投射物
     */
    private cleanupInactiveProjectiles(): void {
        // 使用 filter 创建新数组，只保留活跃的投射物
        const oldLength = this._projectiles.length;

        // 收集不活跃的投射物
        const inactiveProjectiles = this._projectiles.filter(p => !p.isActive);

        // 尝试回收不活跃的投射物
        for (const projectile of inactiveProjectiles) {
            const type = this._projectileTypes.get(projectile);
            if (type) {
                // 直接回收到对象池，不再调用 destroy 方法
                this._projectilePool.recycle(type, projectile);
                this._projectileTypes.delete(projectile);
            }
        }

        // 过滤掉不活跃的投射物
        this._projectiles = this._projectiles.filter(p => p.isActive);

        // 如果有投射物被移除，输出日志
        const removedCount = oldLength - this._projectiles.length;
        if (removedCount > 0) {
            console.log(`[ProjectileManager] 清理了 ${removedCount} 个不活跃的投射物，剩余 ${this._projectiles.length} 个`);

            // 输出对象池统计信息
            const stats = this._projectilePool.getStats();
            if (stats.length > 0) {
                console.log('[ProjectileManager] 对象池统计:');
                for (const stat of stats) {
                    console.log(`  - ${stat.type}: ${stat.count} 个`);
                }
            }
        }
    }

    /**
     * 每帧更新
     * 处理命令队列、更新投射物状态、定期清理不活跃的投射物
     * @param deltaTime 时间增量
     * @param _targets 可选的目标列表，用于碰撞检测（当前未使用）
     */
    public update(deltaTime: number, _targets?: any[]): void {
        // 1. 处理待处理的命令
        this.processPendingCommands();

        // 2. 更新所有活跃的投射物
        for (const projectile of this._projectiles) {
            if (projectile.isActive) {
                projectile.update(deltaTime);

            }
        }

        // 3. 定期清理不活跃的投射物
        this._frameCount++;
        if (this._frameCount >= this.CLEANUP_INTERVAL) {
            this.cleanupInactiveProjectiles();
            this._frameCount = 0;
        }
    }

    /**
     * 获取所有活跃的投射物
     * @returns 活跃的投射物数组
     */
    public getActiveProjectiles(): ProjectileCore[] {
        // 直接返回过滤后的数组，不修改原数组
        return this._projectiles.filter(p => p.isActive);
    }

    /**
     * 清空所有投射物
     * 销毁所有投射物并清空数组
     */
    public clear(): void {
        // 确保正确销毁所有投射物
        for (const projectile of this._projectiles) {
            if (projectile) {
                // 传入 skipRecycle=true 避免循环调用
                projectile.destroy(true);
            }
        }

        // 清空对象池
        this._projectilePool.clear();

        // 清空数组和命令队列
        this._projectiles = [];
        this._pendingCommands = [];
        this._projectileTypes.clear();
        this._frameCount = 0;

        console.log('[ProjectileManager] 已清空所有投射物和对象池');
    }
}