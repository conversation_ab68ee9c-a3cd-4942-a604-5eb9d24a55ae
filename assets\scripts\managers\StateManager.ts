import { GameState } from '../enums/GameState';
import { IGameState } from '../interfaces/IGameState';

export class StateManager {
    private stateMap: Map<GameState, IGameState> = new Map();
    private currentState: GameState = GameState.None;
    private stateTransitions: Map<GameState, GameState[]> = new Map();

    constructor() {

    }
    /**
     * 设置状态可切换表（支持依赖注入或外部配置）
     */
    public setTransitions(transitions: Map<GameState, GameState[]>) {
        this.stateTransitions = transitions;
    }
    registerState(state: GameState, handler: IGameState) {
        this.stateMap.set(state, handler);
    }    changeState(newState: GameState) {
        if (newState === this.currentState) return;
        
        // 确保状态转换表已初始化
        if (!this.stateTransitions) {
            console.error('StateManager: stateTransitions is undefined!');
            return;
        }
        
        // 检查状态转换是否合法
        if (this.currentState !== GameState.None) {
            const allowed = this.stateTransitions.get(this.currentState);
            if (allowed && allowed.indexOf(newState) === -1) {
                console.error(`Invalid state transition from ${this.currentState} to ${newState}`);
                return;
            }
        }
        
        // 获取状态对象
        const currentStateObj = this.stateMap.get(this.currentState);
        const newStateObj = this.stateMap.get(newState);
        
        // 确保目标状态已注册
        if (!newStateObj) {
            console.error(`State ${newState} not found in state map`);
            return;
        }
        
        // 退出当前状态
        if (currentStateObj) {
            try {
                currentStateObj.exit();
            } catch (e) {
                console.error(`Error exiting state ${this.currentState}:`, e);
            }
        }
        
        // 更新当前状态
        this.currentState = newState;
        
        // 进入新状态
        try {
            newStateObj.enter();
        } catch (e) {
            console.error(`Error entering state ${newState}:`, e);
        }
    }

    getCurrentState(): GameState {
        return this.currentState;
    }

    getCurrentStateObj(): IGameState | undefined {
        return this.stateMap.get(this.currentState);
    }

    update(dt: number) {
        const stateObj = this.stateMap.get(this.currentState);
        if (stateObj) stateObj.update(dt);
    }
}
