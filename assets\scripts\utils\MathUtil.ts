import { game } from 'cc';

/** 用于弧度转角度 */
const rad2Deg = 180 / Math.PI;

/** 用于角度转弧度 */
const deg2Rad = Math.PI / 180;

/**
 * 数学工具

 *
 * 扩展了高性能向量计算功能，避免使用 Vec3 对象
 */
export class MathUtil {

    /**
     * 用于弧度转角度
     */
    public static get rad2Deg() {
        return rad2Deg;
    }

    /**
     * 用于角度转弧度
     */
    public static get deg2Rad() {
        return deg2Rad;
    }

    /**
     * 弧度转角度
     * @param radians 弧度值
     */
    public static radiansToDegrees(radians: number) {
        return radians * rad2Deg;
    }

    /**
     * 角度转弧度
     * @param degree
     */
    public static degreesToRadians(degree: number) {
        return degree * deg2Rad;
    }

    /**
     * 限制值
     * @param value 值
     * @param min 最小值
     * @param max 最大值
     */
    public static clamp(value: number, min: number, max: number) {
        if (value < min) {
            return min;
        } else if (value > max) {
            return max;
        }
        return value;
    }

    /**
     * 限制值
     * @param value 值
     */
    public static clamp01(value: number) {
        return MathUtil.clamp(value, 0, 1);
    }

    /**
     * 线性插值
     * @param from
     * @param to
     * @param t
     */
    public static lerp(from: number, to: number, t: number) {
        return from + (to - from) * MathUtil.clamp01(t);
    }

    /**
     * 0 或 1
     * @param a
     * @param t
     */
    public static step(a: number, t: number) {
        return t < a ? 0 : 1;
    }

    /**
     * 在最小值和最大值之间进行插值，并在极限处进行平滑处理
     * @param from
     * @param to
     * @param t
     */
    public static smoothStep(from: number, to: number, t: number) {
        t = MathUtil.clamp01(t);
        t = (-2.0 * t * t * t + 3.0 * t * t);
        return (to * t + from * (1.0 - t));
    }

    /**
     * 平滑控制
     * @param current 当前值
     * @param target 目标值
     * @param currentVelocity 当前速度
     * @param smoothTime 平滑时间
     * @param maxSpeed 最大速度
     * @param deltaTime 时间增量
     */
    public static smoothDamp(current: number, target: number, currentVelocity: number, smoothTime: number, maxSpeed?: number, deltaTime?: number) {
        maxSpeed = maxSpeed != undefined ? maxSpeed : Number.POSITIVE_INFINITY;
        deltaTime = deltaTime != undefined ? deltaTime : game.deltaTime;
        smoothTime = Math.max(0.0001, smoothTime);
        const num1 = 2 / smoothTime;
        const num2 = num1 * deltaTime;
        const num3 = (1 / (1 + num2 + 0.47999998927116394 * num2 * num2 + 0.23499999940395355 * num2 * num2 * num2));
        const num4 = current - target;
        const num5 = target;
        const max = maxSpeed * smoothTime;
        const num6 = MathUtil.clamp(num4, -max, max);
        target = current - num6;
        const num7 = (currentVelocity + num1 * num6) * deltaTime;
        let velocity = (currentVelocity - num1 * num7) * num3;
        let num8 = target + (num6 + num7) * num3;
        if ((num5 - current > 0) === (num8 > num5)) {
            num8 = num5;
            velocity = (num8 - num5) / deltaTime;
        }
        return {
            value: num8,
            velocity: velocity,
        };
    }

    /**
     * 向量点乘
     * @param ax 向量a的x分量
     * @param ay 向量a的y分量
     * @param az 向量a的z分量
     * @param bx 向量b的x分量
     * @param by 向量b的y分量
     * @param bz 向量b的z分量
     * @returns 点乘结果
     */
    public static dot(ax: number, ay: number, az: number, bx: number, by: number, bz: number): number {
        return ax * bx + ay * by + az * bz;
    }

    /**
     * 向量在平面上的投影
     * @param vx 向量的x分量
     * @param vy 向量的y分量
     * @param vz 向量的z分量
     * @param nx 平面法线的x分量
     * @param ny 平面法线的y分量
     * @param nz 平面法线的z分量
     * @param out 输出结果的数组 [x, y, z]
     */
    public static projectOnPlane(
        vx: number, vy: number, vz: number,
        nx: number, ny: number, nz: number,
        out: number[]
    ): void {
        // 计算向量在法线上的投影长度
        const projectionLength = this.dot(vx, vy, vz, nx, ny, nz);
        // 计算向量在平面上的投影
        out[0] = vx - nx * projectionLength;
        out[1] = vy - ny * projectionLength;
        out[2] = vz - nz * projectionLength;
    }

    /**
     * 向量在水平面上的投影（针对 UP 向量 [0,1,0] 优化的特殊版本）
     * @param vx 向量的x分量
     * @param _vy 向量的y分量（会被忽略）
     * @param vz 向量的z分量
     * @param out 输出结果的数组 [x, y, z]
     */
    public static projectOnHorizontalPlane(vx: number, _vy: number, vz: number, out: number[]): void {
        // 对于法线为 [0,1,0] 的平面（水平面），投影就是将 y 分量置为 0
        // _vy 参数被忽略，因为水平面投影总是将 y 设为 0
        out[0] = vx;
        out[1] = 0;
        out[2] = vz;
    }

    /**
     * 计算向量长度
     * @param x 向量的x分量
     * @param y 向量的y分量
     * @param z 向量的z分量
     * @returns 向量长度
     */
    public static vectorLength(x: number, y: number, z: number): number {
        return Math.sqrt(x * x + y * y + z * z);
    }

    /**
     * 计算向量在水平面上的长度（忽略y分量）
     * @param x 向量的x分量
     * @param z 向量的z分量
     * @returns 水平长度
     */
    public static horizontalLength(x: number, z: number): number {
        return Math.sqrt(x * x + z * z);
    }

    /**
     * 归一化向量
     * @param x 向量的x分量
     * @param y 向量的y分量
     * @param z 向量的z分量
     * @param out 输出结果的数组 [x, y, z]
     */
    public static normalize(x: number, y: number, z: number, out: number[]): void {
        const len = this.vectorLength(x, y, z);
        if (len > 0.000001) {
            const invLen = 1 / len;
            out[0] = x * invLen;
            out[1] = y * invLen;
            out[2] = z * invLen;
        } else {
            out[0] = 0;
            out[1] = 0;
            out[2] = 0;
        }
    }

    /**
     * 向量叉乘
     * @param ax 向量a的x分量
     * @param ay 向量a的y分量
     * @param az 向量a的z分量
     * @param bx 向量b的x分量
     * @param by 向量b的y分量
     * @param bz 向量b的z分量
     * @param out 输出结果的数组 [x, y, z]
     */
    public static cross(
        ax: number, ay: number, az: number,
        bx: number, by: number, bz: number,
        out: number[]
    ): void {
        out[0] = ay * bz - az * by;
        out[1] = az * bx - ax * bz;
        out[2] = ax * by - ay * bx;
    }

    /**
     * 计算两个向量的夹角（弧度）
     * @param ax 向量a的x分量
     * @param ay 向量a的y分量
     * @param az 向量a的z分量
     * @param bx 向量b的x分量
     * @param by 向量b的y分量
     * @param bz 向量b的z分量
     * @returns 夹角（弧度）
     */
    public static angle(
        ax: number, ay: number, az: number,
        bx: number, by: number, bz: number
    ): number {
        const lenA = this.vectorLength(ax, ay, az);
        const lenB = this.vectorLength(bx, by, bz);

        if (lenA < 0.000001 || lenB < 0.000001) {
            return 0;
        }

        const dot = this.dot(ax, ay, az, bx, by, bz);
        let cos = dot / (lenA * lenB);

        // 处理浮点精度问题
        cos = Math.min(1, Math.max(-1, cos));

        return Math.acos(cos);
    }

    /**
     * 计算两个向量基于指定轴的夹角（逆时针方向为正方向，值范围 -180 ~ 180）
     * @param ax 向量a的x分量
     * @param ay 向量a的y分量
     * @param az 向量a的z分量
     * @param bx 向量b的x分量
     * @param by 向量b的y分量
     * @param bz 向量b的z分量
     * @param axisx 轴向量的x分量
     * @param axisy 轴向量的y分量
     * @param axisz 轴向量的z分量
     * @returns 有符号夹角（角度制）
     */
    public static signedAngle(
        ax: number, ay: number, az: number,
        bx: number, by: number, bz: number,
        axisx: number, axisy: number, axisz: number
    ): number {
        // 临时数组，用于存储计算结果
        const tempA = [0, 0, 0];
        const tempB = [0, 0, 0];
        const tempCross = [0, 0, 0];

        // 将向量 a 和 b 分别投影到以 axis 为法线的平面上
        this.projectOnPlane(ax, ay, az, axisx, axisy, axisz, tempA);
        this.projectOnPlane(bx, by, bz, axisx, axisy, axisz, tempB);

        // 归一化处理
        this.normalize(tempA[0], tempA[1], tempA[2], tempA);
        this.normalize(tempB[0], tempB[1], tempB[2], tempB);

        // 求出同时垂直于 a 和 b 的法向量
        this.cross(
            tempA[0], tempA[1], tempA[2],
            tempB[0], tempB[1], tempB[2],
            tempCross
        );
        this.normalize(tempCross[0], tempCross[1], tempCross[2], tempCross);

        // 将法向量到 axis 上的投影长度
        // 若投影长度为正值则表示法向量与 axis 同向
        const sign = this.dot(tempCross[0], tempCross[1], tempCross[2], axisx, axisy, axisz);

        // 求出向量 a 和 b 的夹角
        const radian = Math.acos(this.dot(tempA[0], tempA[1], tempA[2], tempB[0], tempB[1], tempB[2]));

        // 混合在一起！
        return radian * sign * rad2Deg;
    }
}
