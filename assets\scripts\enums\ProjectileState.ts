/**
 * 炮弹状态枚举
 */
export enum ProjectileState {
    /** 空闲状态 */
    IDLE = 'idle',
    /** 活跃状态 */
    ACTIVE = 'active',
    /** 碰撞状态 */
    COLLIDED = 'collided',
    /** 爆炸状态 */
    EXPLODING = 'exploding',
    /** 销毁状态 */
    DESTROYED = 'destroyed'
}

/**
 * 敌人状态枚举
 */
export enum EnemyState {
    /** 空闲状态 */
    IDLE = 'idle',
    /** 攻击状态 */
    ATTACKING = 'attacking',
    /** 受伤状态 */
    HURT = 'hurt',
    /** 死亡状态 */
    DEAD = 'dead'
}

/**
 * 武器状态枚举
 */
export enum WeaponState {
    /** 空闲状态 */
    IDLE = 'idle',
    /** 攻击状态 */
    ATTACKING = 'attacking',
    /** 死亡状态 */
    DEAD = 'dead'
}
