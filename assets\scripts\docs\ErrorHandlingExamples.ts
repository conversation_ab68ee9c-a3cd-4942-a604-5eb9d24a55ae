/**
 * 错误处理示例
 * 此文件提供了错误处理系统的使用示例和最佳实践
 * 请参考这些示例在游戏中正确处理错误
 */

import { ErrorManager, LogicError, ResourceError, ErrorSeverity } from '../managers/ErrorManager';

/**
 * 基本错误处理示例
 */
export class BasicErrorHandlingExamples {
    /**
     * 记录基本错误
     */
    static logBasicError(): void {
        try {
            // 模拟操作可能引发错误
            const obj = null;
            obj.someMethod(); // 这将引发错误
        } catch (error) {
            // 记录错误
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'BasicErrorHandlingExamples.logBasicError'
            );
        }
    }
    
    /**
     * 使用自定义错误类型
     */
    static useCustomErrorTypes(): void {
        try {
            // 模拟加载资源失败
            throw new ResourceError('无法加载纹理', 'textures/enemy.png');
        } catch (error) {
            // 记录自定义错误
            ErrorManager.getInstance().logError(error, 'BasicErrorHandlingExamples.useCustomErrorTypes');
        }
    }
    
    /**
     * 不同严重程度的错误
     */
    static logDifferentSeverityLevels(): void {
        // 信息级别 - 不影响游戏，仅供参考
        ErrorManager.getInstance().logInfo(
            '玩家进入新区域',
            'GameZoneManager'
        );
        
        // 警告级别 - 可能影响游戏体验但不严重
        ErrorManager.getInstance().logWarning(
            '音效资源加载延迟',
            'AudioManager'
        );
        
        // 错误级别 - 影响功能但游戏可继续
        ErrorManager.getInstance().logError(
            new Error('无法生成敌人'),
            'EnemySpawner'
        );
        
        // 致命级别 - 游戏无法继续
        // 谨慎使用！这会触发严重错误处理逻辑
        ErrorManager.getInstance().logFatal(
            new Error('游戏场景严重损坏'),
            'SceneManager'
        );
    }
}

/**
 * 高级错误处理示例
 */
export class AdvancedErrorHandlingExamples {
    /**
     * 错误恢复示例
     */
    static errorRecoveryExample(): void {
        try {
            // 模拟复杂操作
            this.complexOperation();
        } catch (error) {
            // 记录错误并获取错误ID
            const errorId = ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'AdvancedErrorHandlingExamples.errorRecoveryExample'
            );
            
            // 尝试恢复
            if (this.tryToRecover()) {
                // 恢复成功，标记错误已解决
                ErrorManager.getInstance().markErrorAsRecovered(
                    errorId,
                    'Successfully recovered by using backup data'
                );
            }
        }
    }
    
    /**
     * 模拟复杂操作
     */
    private static complexOperation(): void {
        // 模拟失败操作
        throw new LogicError('数据处理异常', 'DataProcessor');
    }
    
    /**
     * 模拟恢复尝试
     */
    private static tryToRecover(): boolean {
        // 模拟成功恢复
        return true;
    }
    
    /**
     * 使用 try-catch 包装整个功能模块
     */
    static wrapEntireFeature(): void {
        try {
            // 模拟一个复杂的游戏功能，包含多个可能失败的步骤
            this.step1();
            this.step2();
            this.step3();
        } catch (error) {
            // 使用上下文信息记录错误
            const errorContext = error.message.includes('step1') 
                ? 'Feature.Step1' 
                : error.message.includes('step2')
                    ? 'Feature.Step2'
                    : 'Feature.Step3';
                    
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                errorContext
            );
            
            // 优雅降级
            this.fallbackImplementation();
        }
    }
    
    private static step1(): void { /* 实现省略 */ }
    private static step2(): void { /* 实现省略 */ }
    private static step3(): void { /* 实现省略 */ }
    private static fallbackImplementation(): void { /* 实现省略 */ }
}

/**
 * 错误处理最佳实践
 */
export class ErrorHandlingBestPractices {
    /**
     * 1. 在关键功能入口处使用 try-catch
     */
    static criticalFunctionWithErrorHandling(param: any): void {
        try {
            // 实际功能实现
            this.doActualWork(param);
        } catch (error) {
            // 详细记录上下文和参数信息（注意避免记录敏感数据）
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                `ErrorHandlingBestPractices.criticalFunction(param=${JSON.stringify(param)})`
            );
            
            // 返回安全的失败状态或默认值
            // return defaultValue;
        }
    }
    
    private static doActualWork(param: any): void {
        // 实际工作...
    }
    
    /**
     * 2. 在异步代码中正确处理错误
     */
    static async asyncFunctionWithErrorHandling(): Promise<void> {
        try {
            await this.asyncOperation();
        } catch (error) {
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'ErrorHandlingBestPractices.asyncFunction'
            );
            
            // 重新抛出或返回错误状态
            throw error;
        }
    }
    
    private static async asyncOperation(): Promise<void> {
        // 异步操作...
    }
    
    /**
     * 3. 组合多个错误处理策略
     */
    static combinedErrorHandlingStrategy(): void {
        // 1. 使用条件检查避免错误
        if (!this.preconditionCheck()) {
            ErrorManager.getInstance().logWarning(
                '操作前置条件不满足，跳过执行',
                'ErrorHandlingBestPractices.combinedStrategy'
            );
            return;
        }
        
        try {
            // 2. 尝试主要实现
            this.primaryImplementation();
        } catch (error) {
            // 3. 记录错误
            ErrorManager.getInstance().logError(
                error instanceof Error ? error : new Error(String(error)),
                'ErrorHandlingBestPractices.primaryImplementation'
            );
            
            try {
                // 4. 尝试备用实现
                this.fallbackImplementation();
            } catch (fallbackError) {
                // 5. 如果备用也失败，记录更严重的错误
                ErrorManager.getInstance().logError(
                    new Error(`主实现和备用实现均失败: ${fallbackError.message}`),
                    'ErrorHandlingBestPractices.fallbackImplementation'
                );
            }
        }
    }
    
    private static preconditionCheck(): boolean { return true; }
    private static primaryImplementation(): void { /* 主要实现 */ }
    private static fallbackImplementation(): void { /* 备用实现 */ }
}
