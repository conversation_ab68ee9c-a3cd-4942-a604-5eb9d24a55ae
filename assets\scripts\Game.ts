import { IGameManager } from './interfaces/IManager';

import { GameState } from './enums/GameState';

import {
    InitializingState,
    PlayingState,
    PausedState,
    GameOverState,
    ResmueState
} from './states/GameStates';
import { Component, Prefab, Vec3, _decorator } from 'cc';

import { GameEvent, GameEventMap } from './events/GameEvents';


import { EventManager } from './managers/EventManager';
import { Inject } from './decorators/Inject';
import { CannonWeapon } from './entities/weapons/CannonWeapon';
import { DependencyContainer } from './containers/DependencyContainer';
import { ZombieEnemy } from './entities/enemies/ZombieEnemy';
import { gameServiceConfigs } from './configs/ServiceConfigs';
import { EntityManager } from './managers/EntityManager';
import { StateManager } from './managers/StateManager';
import { EffectManager } from './managers/EffectManager';
import { ErrorManager } from './managers/ErrorManager';

const { ccclass, property } = _decorator;

/**
 * 游戏主控制器
 * 负责游戏主流程、依赖注入、状态机调度、实体管理等
 */
@ccclass('Game')
export class Game extends Component {
    /** 游戏累计时间（秒） */
    private _gameTime: number = 0;
    /** 游戏是否已初始化 */
    private _isInitialized: boolean = false;
    /** 事件管理器（依赖注入） */
    @Inject('eventManager')
    private _eventManager: EventManager<GameEventMap>;
    /** 游戏管理器（依赖注入） */
    @Inject('gameManager')
    private _gameManager: IGameManager = null;
    /** 实体管理器（依赖注入，统一管理武器/敌人等） */
    @Inject('entityManager')
    private _entityManager: EntityManager;
    /** 状态机管理器（依赖注入，负责状态切换与调度） */
    @Inject('stateManager')
    private _stateManager: StateManager;
    /** 效果管理器（依赖注入） */
    @Inject('effectManager')
    private _effectManager: EffectManager;
    /** 武器预制体 */
    @property(Prefab)
    private pfbWeapon: Prefab = null;
    /** 子弹预制体 */
    @property(Prefab)
    private pfbProjectile: Prefab = null;
    /** 子弹预制体 */
    @property(Prefab)
    private pfbBulletExplosion: Prefab = null;
    /** 敌人预制体 */
    @property(Prefab)
    private pfbEnemy: Prefab = null;
    /** 怪物出生点 */
    private _spawnPos: Vec3 = new Vec3(10, 0, 0);

    /**
     * 游戏加载与初始化
     */
    async onLoad() {
        try {
            await this.initializeDependencies();

            // 初始化错误管理器
            ErrorManager.getInstance().init(this._eventManager);

            this.initializeStateTransitions();
            this.initializeStates();
            this.registerEventListeners();
            this.initializeEntities();
            this.initializeEffects();
            this._stateManager.changeState(GameState.INITIALIZING);

            // 触发游戏开始事件
            this._eventManager.emit(GameEvent.GAME_START, { timestamp: Date.now() });
        } catch (error) {
            console.error('游戏初始化失败:', error);
            // 即使错误管理器尚未初始化，也尝试记录错误
            ErrorManager.getInstance().logFatal(
                error instanceof Error ? error : new Error(String(error)),
                'Game.onLoad'
            );
        }
    }

    /**
     * 初始化依赖容器
     */
    private async initializeDependencies(): Promise<void> {
        const container = DependencyContainer.getInstance();
        container.registerFromConfig(gameServiceConfigs);
        await container.initialize();
    }

    /**
     * 配置状态转换表
     */
    private initializeStateTransitions(): void {
        this._stateManager.setTransitions(new Map([
            [GameState.INITIALIZING, [GameState.PLAYING, GameState.PAUSED]],
            [GameState.PLAYING, [GameState.PAUSED, GameState.GAME_OVER]],
            [GameState.PAUSED, [GameState.RESUME, GameState.GAME_OVER]],
            [GameState.RESUME, [GameState.PLAYING]],
            [GameState.GAME_OVER, [GameState.INITIALIZING]]
        ]));
    }

    /**
     * 注册所有游戏状态到状态机
     */
    private initializeStates(): void {
        this._stateManager.registerState(GameState.INITIALIZING, new InitializingState(this));
        this._stateManager.registerState(GameState.PLAYING, new PlayingState(this));
        this._stateManager.registerState(GameState.PAUSED, new PausedState(this));
        this._stateManager.registerState(GameState.RESUME, new ResmueState(this));
        this._stateManager.registerState(GameState.GAME_OVER, new GameOverState(this));
    }

    /**
     * 注册全局事件监听器，驱动状态切换
     */
    private registerEventListeners(): void {
        this._eventManager.on(GameEvent.GAME_START, () => {
            this._stateManager.changeState(GameState.PLAYING);
        }, this);
        this._eventManager.on(GameEvent.GAME_PAUSE, () => {
            this._stateManager.changeState(GameState.PAUSED);
        }, this);
        this._eventManager.on(GameEvent.GAME_RESUME, () => {
            this._stateManager.changeState(GameState.RESUME);
        }, this);
        this._eventManager.on(GameEvent.GAME_OVER, () => {
            this._stateManager.changeState(GameState.GAME_OVER);
        }, this);
    }

    /**
     * 移除所有事件监听（生命周期钩子）
     */
    removeEventListeners() {
        this._eventManager.offAllForOwner(this);
    }

    /** 当前游戏状态 */
    get currentState(): GameState {
        return this._stateManager.getCurrentState();
    }
    /** 是否已初始化 */
    get isInitialized(): boolean {
        return this._isInitialized;
    }
    /** 游戏累计时间 */
    get gameTime(): number {
        return this._gameTime;
    }
    /** 游戏管理器 */
    get gameManager(): IGameManager {
        return this._gameManager;
    }
    /** 切换游戏状态 */
    public changeState(newState: GameState): void {
        this._stateManager.changeState(newState);
    }
    /** 标记初始化完成 */
    public setInitialized(): void {
        this._isInitialized = true;
    }
    /** 增加游戏时间 */
    public setGameTime(dt: number): void {
        this._gameTime += dt;
    }
    /** 清空游戏时间 */
    public clearGameTime(): void {
        this._gameTime = 0;
    }
    /**
     * 每帧更新，驱动状态机
     * @param deltaTime 帧间隔时间（秒）
     */
    public update(deltaTime: number): void {
        this._stateManager.update(deltaTime);
    }
    /**
     * 组件销毁时自动移除事件监听
     */
    protected onDestroy(): void {
        this.removeEventListeners();
    }    /**
     * 初始化实体（武器、敌人等）
     */
    private initializeEntities(): void {
        // 初始化武器系统
        this.initializeWeapons();

        // 初始化敌人系统
        this.initializeEnemies();
    }

    /**
     * 初始化特效系统
     */
    private initializeEffects(): void {
        // 注册子弹爆炸预制体到效果管理器
        if (this._effectManager && this.pfbBulletExplosion) {
            this._effectManager.setBulletExplosionPrefab(this.pfbBulletExplosion);
        }
    }

    /**
     * 初始化武器系统
     */
    private initializeWeapons(): void {
        if (!this.pfbWeapon || !this.pfbProjectile) {
            console.error('Game: 武器或子弹预制体未设置！');
            return;
        }

        // 创建一个炮塔武器示例
        const cannon = new CannonWeapon(
            this.pfbWeapon,
            this.node,
            this.pfbProjectile,
            this.node,
            { x: 0, y: 0, z: 0 }
        );

        // 将武器添加到战斗管理器，而不是实体管理器
        this._gameManager.addWeapon(cannon);

        console.log("武器已添加到游戏管理器");
    }

    /**
     * 初始化敌人系统
     */
    private initializeEnemies(): void {
        if (!this.pfbEnemy) {
            console.error('Game: 敌人预制体未设置！');
            return;
        }

        // 敌人的移动路径 - 确保路径点之间有足够的距离
        const path = [
            new Vec3(0, 0, 0),    // 起点
            new Vec3(5, 0, 0),    // 向右5单位
 
         
        ];

        console.log("设置敌人路径:", path.map(p => `(${p.x}, ${p.y}, ${p.z})`).join(" -> "));

        // 创建一个敌人示例
        const enemy = new ZombieEnemy(
            path,
            10000, // 血量
            1,   // 攻击速度
            10,  // 攻击伤害
            5,   // 防御
            2,   // 移动速度 - 增加移动速度，方便测试
            50,  // 奖励
            this.pfbEnemy,
            this.node,
            this._gameManager
        );

        // 将敌人添加到游戏管理器，游戏管理器会将敌人转发到战斗管理器
        // 注意：这里直接使用已创建的敌人实例，而不是使用工厂函数
        this._gameManager.addEnemy(() => enemy);

        // 输出调试信息
        console.log("敌人已添加到游戏管理器");
    }
}