import { ErrorManager, ErrorSeverity, GameError } from '../managers/ErrorManager';
import { LogFileManager } from './LogFileManager';

/**
 * 错误模式类型
 */
export enum ErrorPattern {
    INVALID_STATE_TRANSITION = 'invalid_state_transition',
    NULL_REFERENCE = 'null_reference',
    OUT_OF_BOUNDS = 'out_of_bounds',
    RESOURCE_LOADING = 'resource_loading',
    PHYSICS_ERROR = 'physics_error',
    NETWORK_ERROR = 'network_error',
    COMPONENT_ERROR = 'component_error',
    ANIMATION_ERROR = 'animation_error',
    RENDERING_ERROR = 'rendering_error',
    UNKNOWN = 'unknown'
}

/**
 * 错误诊断结果
 */
export interface ErrorDiagnosis {
    pattern: ErrorPattern;
    suggestedFix: string;
    priority: number; // 1-10, 10为最高优先级
    autoRecoverable: boolean;
    frequency?: number; // 错误发生频率
    relatedContexts?: string[]; // 相关上下文
    firstOccurrence?: number; // 首次出现时间
    lastOccurrence?: number; // 最后出现时间
}

/**
 * 错误分析器
 * 负责分析错误并提供诊断信息
 */
export class ErrorAnalyzer {
    private static _instance: ErrorAnalyzer;
    private _knownPatterns: Map<RegExp, ErrorDiagnosis> = new Map();
    private _logManager: LogFileManager = null;
    
    // 错误历史记录 - 用于分析错误频率
    private _errorHistory: Map<string, {
        count: number,
        firstOccurrence: number,
        lastOccurrence: number,
        contexts: Set<string>,
        diagnosis: ErrorDiagnosis
    }> = new Map();
    
    // 热点错误追踪 - 记录最频繁出现的错误
    private _hotspotErrors: Array<{
        errorSignature: string,
        count: number,
        pattern: ErrorPattern
    }> = [];
    
    // 最大热点错误数量
    private readonly _maxHotspotErrors: number = 10;
    
    /**
     * 获取单例
     */
    public static getInstance(): ErrorAnalyzer {
        if (!this._instance) {
            this._instance = new ErrorAnalyzer();
            this._instance.registerKnownPatterns();
            // 确保 LogManager 初始化
            this._instance._logManager = LogFileManager.getInstance();
        }
        return this._instance;
    }
    
    /**
     * 注册已知的错误模式
     */
    private registerKnownPatterns(): void {
        // 状态转换错误
        this._knownPatterns.set(
            /Cannot transition from '(\w+)' to '(\w+)'/i,
            {
                pattern: ErrorPattern.INVALID_STATE_TRANSITION,
                suggestedFix: '检查状态转换条件和状态机配置',
                priority: 8,
                autoRecoverable: true
            }
        );
        
        // 空引用错误
        this._knownPatterns.set(
            /(?:null|undefined) is not an object|Cannot read propert(?:y|ies) of (?:null|undefined)/i,
            {
                pattern: ErrorPattern.NULL_REFERENCE,
                suggestedFix: '检查对象初始化和空值检查',
                priority: 10,
                autoRecoverable: false
            }
        );
        
        // 数组越界
        this._knownPatterns.set(
            /index out of bounds|invalid index/i,
            {
                pattern: ErrorPattern.OUT_OF_BOUNDS,
                suggestedFix: '检查数组访问和边界条件',
                priority: 9,
                autoRecoverable: true
            }
        );
        
        // 资源加载错误
        this._knownPatterns.set(
            /(?:failed to load|could not load|resource not found)/i,
            {
                pattern: ErrorPattern.RESOURCE_LOADING,
                suggestedFix: '检查资源路径和资源加载逻辑',
                priority: 7,
                autoRecoverable: true
            }
        );
        
        // 物理计算错误
        this._knownPatterns.set(
            /(?:physics|collision|rigid body|raycast) (?:error|exception|invalid)/i,
            {
                pattern: ErrorPattern.PHYSICS_ERROR,
                suggestedFix: '检查物理参数和碰撞设置',
                priority: 6,
                autoRecoverable: true
            }
        );
        
        // 组件错误 - 找不到组件或节点无效
        this._knownPatterns.set(
            /(?:component not found|node is invalid|component is null|not attached)/i,
            {
                pattern: ErrorPattern.COMPONENT_ERROR,
                suggestedFix: '检查组件/节点引用和生命周期',
                priority: 9,
                autoRecoverable: false
            }
        );
        
        // 动画错误
        this._knownPatterns.set(
            /(?:animation|animator|animation clip|animation state) (?:error|not found|invalid)/i,
            {
                pattern: ErrorPattern.ANIMATION_ERROR,
                suggestedFix: '检查动画配置、动画状态和过渡',
                priority: 7,
                autoRecoverable: true
            }
        );
        
        // 渲染错误
        this._knownPatterns.set(
            /(?:render|material|shader|texture|mesh) (?:error|invalid|failed)/i,
            {
                pattern: ErrorPattern.RENDERING_ERROR,
                suggestedFix: '检查材质、纹理和渲染设置',
                priority: 8,
                autoRecoverable: true
            }
        );
    }
    
    /**
     * 分析错误
     * @param error 错误对象
     * @param context 错误上下文
     * @returns 错误诊断结果
     */
    public analyzeError(error: Error, context: string): ErrorDiagnosis {
        if (!error) {
            return {
                pattern: ErrorPattern.UNKNOWN,
                suggestedFix: '无效的错误对象',
                priority: 5,
                autoRecoverable: false
            };
        }
        
        const errorMessage = `${error.message || ''} ${error.stack || ''}`;
        const errorSignature = `${error.name || 'Unknown'}:${error.message || 'No message'}`;
        
        // 检查是否匹配已知模式
        let diagnosis: ErrorDiagnosis = null;
        
        for (const [pattern, knownDiagnosis] of this._knownPatterns.entries()) {
            if (pattern.test(errorMessage)) {
                diagnosis = { ...knownDiagnosis };
                break;
            }
        }
        
        // 如果没有匹配的模式，返回未知错误
        if (!diagnosis) {
            diagnosis = {
                pattern: ErrorPattern.UNKNOWN,
                suggestedFix: '检查错误日志以获取更多信息',
                priority: 5,
                autoRecoverable: false
            };
        }
        
        // 更新错误历史
        this.updateErrorHistory(errorSignature, diagnosis, context);
        
        // 记录日志
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `错误分析 - 模式: ${diagnosis.pattern}, 建议: ${diagnosis.suggestedFix}`,
                    'INFO',
                    'ErrorAnalyzer.analyzeError'
                );
            } catch (e) {
                console.error('写入错误分析日志失败:', e);
            }
        }
        
        return diagnosis;
    }
    
    /**
     * 更新错误历史记录
     */
    private updateErrorHistory(
        errorSignature: string,
        diagnosis: ErrorDiagnosis,
        context: string
    ): void {
        const now = Date.now();
        
        if (this._errorHistory.has(errorSignature)) {
            // 更新已存在的错误记录
            const record = this._errorHistory.get(errorSignature);
            if (record) {
                record.count++;
                record.lastOccurrence = now;
                record.contexts.add(context);
                
                // 更新频率信息
                const timeSpan = (now - record.firstOccurrence) / 1000; // 秒
                if (timeSpan > 0) {
                    diagnosis.frequency = record.count / timeSpan;
                }
            }
        } else {
            // 创建新的错误记录
            this._errorHistory.set(errorSignature, {
                count: 1,
                firstOccurrence: now,
                lastOccurrence: now,
                contexts: new Set([context]),
                diagnosis
            });
            
            diagnosis.frequency = 1;
        }
        
        // 更新热点错误
        this.updateHotspotErrors(errorSignature, diagnosis.pattern);
    }
    
    /**
     * 更新热点错误列表
     */
    private updateHotspotErrors(errorSignature: string, pattern: ErrorPattern): void {
        const record = this._errorHistory.get(errorSignature);
        
        if (!record) return;
        
        // 查找是否已在热点列表中
        const existingIndex = this._hotspotErrors.findIndex(
            item => item.errorSignature === errorSignature
        );
        
        if (existingIndex >= 0) {
            // 更新现有的热点记录
            this._hotspotErrors[existingIndex].count = record.count;
        } else {
            // 添加新的热点记录
            this._hotspotErrors.push({
                errorSignature,
                count: record.count,
                pattern
            });
            
            // 排序并保持最大数量
            this._hotspotErrors.sort((a, b) => b.count - a.count);
            
            if (this._hotspotErrors.length > this._maxHotspotErrors) {
                this._hotspotErrors.pop();
            }
        }
    }
    
    /**
     * 尝试自动修复错误
     * @param error 错误对象
     * @param context 错误上下文
     * @returns 是否成功修复
     */
    public attemptAutoFix(error: Error, context: string): boolean {
        if (!error) return false;
        
        const diagnosis = this.analyzeError(error, context);
        
        // 如果错误不可自动恢复，返回false
        if (!diagnosis.autoRecoverable) {
            return false;
        }
        
        try {
            switch (diagnosis.pattern) {
                case ErrorPattern.INVALID_STATE_TRANSITION:
                    // 尝试通用状态机恢复策略
                    return this.fixStateTransitionError(error, context);
                    
                case ErrorPattern.OUT_OF_BOUNDS:
                    // 尝试修复索引错误
                    return this.fixOutOfBoundsError(error, context);
                    
                case ErrorPattern.RESOURCE_LOADING:
                    // 尝试资源加载错误恢复
                    return this.fixResourceLoadingError(error, context);
                    
                case ErrorPattern.COMPONENT_ERROR:
                    // 尝试修复组件错误
                    return this.fixComponentError(error, context);
                    
                case ErrorPattern.ANIMATION_ERROR:
                    // 尝试修复动画错误
                    return this.fixAnimationError(error, context);
                    
                case ErrorPattern.RENDERING_ERROR:
                    // 尝试修复渲染错误
                    return this.fixRenderingError(error, context);
                    
                default:
                    return false;
            }
        } catch (e) {
            console.error('尝试自动修复时发生错误:', e);
            return false;
        }
    }
    
    /**
     * 修复状态转换错误
     */
    private fixStateTransitionError(error: Error, context: string): boolean {
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `尝试修复状态转换错误: ${error.message}`,
                    'INFO',
                    'ErrorAnalyzer.fixStateTransitionError'
                );
            } catch (e) {
                console.error('写入日志失败:', e);
            }
        }
        return false; // 实际项目中可以实现真正的修复逻辑
    }
    
    /**
     * 修复数组越界错误
     */
    private fixOutOfBoundsError(error: Error, context: string): boolean {
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `尝试修复数组越界错误: ${error.message}`,
                    'INFO',
                    'ErrorAnalyzer.fixOutOfBoundsError'
                );
            } catch (e) {
                console.error('写入日志失败:', e);
            }
        }
        return false; // 实际项目中可以实现真正的修复逻辑
    }
    
    /**
     * 修复资源加载错误
     */
    private fixResourceLoadingError(error: Error, context: string): boolean {
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `尝试修复资源加载错误: ${error.message}`,
                    'INFO',
                    'ErrorAnalyzer.fixResourceLoadingError'
                );
            } catch (e) {
                console.error('写入日志失败:', e);
            }
        }
        return false; // 实际项目中可以实现真正的修复逻辑
    }
    
    /**
     * 修复组件错误
     */
    private fixComponentError(error: Error, context: string): boolean {
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `尝试修复组件错误: ${error.message}`,
                    'INFO',
                    'ErrorAnalyzer.fixComponentError'
                );
            } catch (e) {
                console.error('写入日志失败:', e);
            }
        }
        return false; // 实际项目中可以实现真正的修复逻辑
    }
    
    /**
     * 修复动画错误
     */
    private fixAnimationError(error: Error, context: string): boolean {
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `尝试修复动画错误: ${error.message}`,
                    'INFO',
                    'ErrorAnalyzer.fixAnimationError'
                );
            } catch (e) {
                console.error('写入日志失败:', e);
            }
        }
        return false; // 实际项目中可以实现真正的修复逻辑
    }
    
    /**
     * 修复渲染错误
     */
    private fixRenderingError(error: Error, context: string): boolean {
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `尝试修复渲染错误: ${error.message}`,
                    'INFO',
                    'ErrorAnalyzer.fixRenderingError'
                );
            } catch (e) {
                console.error('写入日志失败:', e);
            }
        }
        return false; // 实际项目中可以实现真正的修复逻辑
    }
    
    /**
     * 建议恢复操作
     * @param error 错误对象
     * @param context 错误上下文
     * @returns 建议操作
     */
    public suggestRecoveryAction(error: Error, context: string): string {
        if (!error) return '无效的错误对象';
        const diagnosis = this.analyzeError(error, context);
        return diagnosis.suggestedFix;
    }
    
    /**
     * 获取热点错误列表
     * 返回频繁出现的错误
     */
    public getHotspotErrors(): Array<{
        errorSignature: string,
        count: number,
        pattern: ErrorPattern
    }> {
        return [...this._hotspotErrors];
    }
    
    /**
     * 获取错误频率统计
     * 返回各种错误模式的频率
     */
    public getErrorPatternFrequencies(): Map<ErrorPattern, number> {
        const frequencies = new Map<ErrorPattern, number>();
        
        // 初始化所有错误模式的频率为0
        // 由于Object.values在旧版TS中不支持，使用枚举的遍历方式
        const patterns = Object.keys(ErrorPattern)
            .filter(key => isNaN(Number(key)))
            .map(key => (ErrorPattern as any)[key] as ErrorPattern);
            
        patterns.forEach(pattern => {
            frequencies.set(pattern, 0);
        });
        
        // 统计每种错误模式的总数
        for (const record of this._errorHistory.values()) {
            const pattern = record.diagnosis.pattern;
            frequencies.set(pattern, (frequencies.get(pattern) || 0) + record.count);
        }
        
        return frequencies;
    }
    
    /**
     * 生成错误分析报告
     * 用于开发调试
     */
    public generateErrorReport(): string {
        try {
            const now = new Date();
            const reportLines = [
                `=== 错误分析报告 (${now.toLocaleString()}) ===`,
                ''
            ];
            
            // 添加热点错误
            reportLines.push('== 热点错误 (频繁出现) ==');
            if (this._hotspotErrors.length > 0) {
                this._hotspotErrors.forEach((error, index) => {
                    reportLines.push(`${index + 1}. 模式: ${error.pattern}, 出现次数: ${error.count}, 签名: ${error.errorSignature}`);
                });
            } else {
                reportLines.push('无热点错误');
            }
            reportLines.push('');
            
            // 添加错误模式频率
            reportLines.push('== 错误模式统计 ==');
            const frequencies = this.getErrorPatternFrequencies();
            let totalErrors = 0;
            
            for (const [pattern, count] of frequencies.entries()) {
                totalErrors += count;
                reportLines.push(`${pattern}: ${count}次`);
            }
            reportLines.push(`总计: ${totalErrors}次错误`);
            reportLines.push('');
            
            // 错误上下文相关性分析
            reportLines.push('== 错误上下文分析 ==');
            const contextMap = new Map<string, number>();
            
            for (const record of this._errorHistory.values()) {
                for (const context of record.contexts) {
                    contextMap.set(context, (contextMap.get(context) || 0) + record.count);
                }
            }
            
            // 按出错次数排序上下文
            const sortedContexts = Array.from(contextMap.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10); // 只显示前10个
            
            if (sortedContexts.length > 0) {
                sortedContexts.forEach(([context, count], index) => {
                    reportLines.push(`${index + 1}. ${context}: ${count}次错误`);
                });
            } else {
                reportLines.push('无上下文数据');
            }
            
            return reportLines.join('\n');
        } catch (e) {
            console.error('生成错误报告时出错:', e);
            return `错误报告生成失败: ${e.message || '未知错误'}`;
        }
    }
}
