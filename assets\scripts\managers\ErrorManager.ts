import { _decorator } from 'cc';
import { EventManager } from './EventManager';
import { GameEvent, GameEventMap } from '../events/GameEvents';
import { ErrorAnalyzer } from '../utils/ErrorAnalyzer';
import { LogFileManager } from '../utils/LogFileManager';

const { ccclass } = _decorator;

/**
 * 游戏错误类型基类
 */
export class GameError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'GameError';
        // 确保堆栈跟踪指向正确位置
        // Error.captureStackTrace?.(this, this.constructor);
    }
}

/**
 * 资源加载错误
 */
export class ResourceError extends GameError {
    constructor(message: string, public resourceId: string) {
        super(`Resource error (${resourceId}): ${message}`);
        this.name = 'ResourceError';
    }
}

/**
 * 逻辑错误
 */
export class LogicError extends GameError {
    constructor(message: string, public moduleName: string) {
        super(`Logic error in ${moduleName}: ${message}`);
        this.name = 'LogicError';
    }
}

/**
 * 网络错误
 */
export class NetworkError extends GameError {
    constructor(message: string, public statusCode?: number) {
        super(`Network error${statusCode ? ` (${statusCode})` : ''}: ${message}`);
        this.name = 'NetworkError';
    }
}

/**
 * 状态错误，用于状态机相关错误
 */
export class StateError extends GameError {
    constructor(message: string, public currentState: string, public targetState: string) {
        super(`State error (${currentState} -> ${targetState}): ${message}`);
        this.name = 'StateError';
    }
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
    INFO,    // 仅信息，不影响游戏
    WARNING, // 警告，可能影响游戏体验
    ERROR,   // 错误，影响功能但游戏可继续
    FATAL    // 致命错误，游戏无法继续
}

/**
 * 错误统计信息
 */
interface ErrorStats {
    totalErrors: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    byContext: Record<string, number>;
    // 错误率计算：错误数 / 游戏时长(分钟)
    errorRate: number;
    // 游戏开始时间
    gameStartTime: number;
}

/**
 * 错误管理器
 * 负责集中处理游戏中的所有错误
 */
@ccclass('ErrorManager')
export class ErrorManager {
    private static _instance: ErrorManager;
    private _eventManager: EventManager<GameEventMap> = null;
    private _isInitialized: boolean = false;
    private _errors: Map<string, { error: Error, timestamp: number, severity: ErrorSeverity, context: string }> = new Map();
    private _errorCount: number = 0;
    private _lastErrorId: string = '';
    private _errorStats: ErrorStats = {
        totalErrors: 0,
        byType: {},
        bySeverity: {},
        byContext: {},
        errorRate: 0,
        gameStartTime: Date.now()
    };
    
    // 最大错误历史记录数
    private _maxErrorHistorySize: number = 100;
    // 错误历史
    private _errorHistory: Array<{
        id: string,
        error: Error,
        timestamp: number,
        severity: ErrorSeverity,
        context: string,
        recovered: boolean
    }> = [];
    
    // 自动恢复配置
    private _autoRecoverableErrors: Set<string> = new Set([
        'TypeError',
        'ReferenceError'
    ]);
    
    // 错误频率限制（防止同一错误在短时间内重复记录）
    private _throttledErrors: Map<string, number> = new Map();
    private _throttleDuration: number = 5000; // 5秒内相同错误只记录一次
    
    // 日志管理器
    private _logManager: LogFileManager = null;

    /**
     * 获取单例
     */
    public static getInstance(): ErrorManager {
        if (!this._instance) {
            this._instance = new ErrorManager();
        }
        return this._instance;
    }
    
    /**
     * 初始化错误管理器
     * @param eventManager 事件管理器实例
     * @param enableLogging 是否启用日志记录
     */
    public init(eventManager: EventManager<GameEventMap>, enableLogging: boolean = true): void {
        if (this._isInitialized) return;

        this._eventManager = eventManager;
        this._isInitialized = true;
        this._errorStats.gameStartTime = Date.now();

        // 初始化日志管理器
        this._logManager = LogFileManager.getInstance();
        this._logManager.init(!!enableLogging);  // 确保参数是布尔值

        // 设置全局错误处理
        this.setupGlobalErrorHandlers();

        console.log('[ErrorManager] Initialized');
        this._logManager.writeToLog('错误管理器初始化完成', 'INFO', 'ErrorManager.init');
    }

    /**
     * 设置全局错误处理器
     */
    private setupGlobalErrorHandlers(): void {
        if (typeof window !== 'undefined') {
            // 处理未捕获的异常
            window.onerror = (message, source, lineno, colno, error) => {
                this.handleError(
                    error || new Error(String(message)),
                    `GlobalError at ${source}:${lineno}:${colno}`,
                    ErrorSeverity.ERROR
                );
                return true; // 阻止默认错误处理
            };

            // 处理未处理的Promise异常
            window.addEventListener('unhandledrejection', (event) => {
                this.handleError(
                    event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
                    'UnhandledPromiseRejection',
                    ErrorSeverity.ERROR
                );
            });
        }
    }

    /**
     * 处理错误
     * @param error 错误对象
     * @param context 错误上下文
     * @param severity 错误严重程度
     * @returns 错误ID
     */
    public handleError(error: Error, context: string, severity: ErrorSeverity = ErrorSeverity.ERROR): string {
        if (!error) {
            console.error('收到无效的错误对象');
            error = new Error('未知错误');
        }
        
        // 检查是否需要节流
        const errorKey = `${error.name || 'Unknown'}:${error.message || ''}:${context}`;
        const now = Date.now();
        
        if (this._throttledErrors.has(errorKey)) {
            const lastTime = this._throttledErrors.get(errorKey);
            if (lastTime && now - lastTime < this._throttleDuration) {
                // 在节流时间内，不重复记录
                return null;
            }
        }
        
        // 更新节流时间
        this._throttledErrors.set(errorKey, now);
        
        // 生成错误ID
        const timestamp = now;
        const errorId = `error-${timestamp}-${this._errorCount++}`;
        
        // 记录错误
        this._errors.set(errorId, { error, timestamp, severity, context });
        this._lastErrorId = errorId;
        
        // 更新错误统计
        this.updateErrorStats(error, context, severity);
        
        // 添加到错误历史
        this.addToErrorHistory(errorId, error, timestamp, severity, context);
        
        // 记录到控制台
        this.logErrorToConsole(error, context, severity);
        
        // 写入日志文件
        if (this._logManager && this._isInitialized) {
            try {
                this._logManager.writeErrorToLog(error, severity, context);
            } catch (e) {
                console.error('写入错误日志失败:', e);
            }
        }
        
        // 使用错误分析器分析错误
        try {
            const analyzer = ErrorAnalyzer.getInstance();
            const diagnosis = analyzer.analyzeError(error, context);
            
            // 如果错误可自动修复，尝试修复
            if (diagnosis && diagnosis.autoRecoverable) {
                const fixed = analyzer.attemptAutoFix(error, context);
                if (fixed) {
                    // 如果修复成功，标记为已恢复
                    this.markErrorAsRecovered(errorId, `自动修复: ${diagnosis.pattern}`);
                    
                    // 记录修复成功日志
                    if (this._logManager) {
                        this._logManager.writeToLog(
                            `自动修复成功: ${diagnosis.pattern} - ${error.message}`,
                            'INFO',
                            'ErrorManager.handleError.autoFix'
                        );
                    }
                }
            }
            
            // 发送错误事件
            if (this._eventManager && this._isInitialized) {
                this._eventManager.emit(GameEvent.ERROR_OCCURRED, {
                    error,
                    context,
                    isFatal: severity === ErrorSeverity.FATAL,
                    timestamp,
                    diagnosis
                });
            }
        } catch (e) {
            console.error('错误分析与处理失败:', e);
        }
        
        // 处理致命错误
        if (severity === ErrorSeverity.FATAL) {
            this.handleFatalError(error, context);
        }
        
        // 对于某些类型的错误，尝试自动恢复
        if (this.canAutoRecover(error)) {
            setTimeout(() => {
                this.markErrorAsRecovered(errorId, 'AutoRecovered');
            }, 100);
        }
        
        return errorId;
    }
    
    /**
     * 检查错误是否可以自动恢复
     */
    private canAutoRecover(error: Error): boolean {
        if (!error || !error.name) return false;
        return this._autoRecoverableErrors.has(error.name);
    }
    
    /**
     * 添加到错误历史
     */
    private addToErrorHistory(id: string, error: Error, timestamp: number, severity: ErrorSeverity, context: string): void {
        if (!id || !error) return;
        
        // 添加到历史记录
        this._errorHistory.push({
            id,
            error,
            timestamp,
            severity,
            context,
            recovered: false
        });
        
        // 限制历史记录大小
        if (this._errorHistory.length > this._maxErrorHistorySize) {
            this._errorHistory.shift();
        }
    }
    
    /**
     * 更新错误统计
     */
    private updateErrorStats(error: Error, context: string, severity: ErrorSeverity): void {
        if (!error) return;
        
        // 增加总错误数
        this._errorStats.totalErrors++;
        
        // 按类型统计
        const errorType = error.name || 'Unknown';
        this._errorStats.byType[errorType] = (this._errorStats.byType[errorType] || 0) + 1;
        
        // 按严重程度统计
        const severityName = ErrorSeverity[severity] || 'UNKNOWN';
        this._errorStats.bySeverity[severityName] = (this._errorStats.bySeverity[severityName] || 0) + 1;
        
        // 按上下文统计
        const safeContext = context || 'Unknown';
        this._errorStats.byContext[safeContext] = (this._errorStats.byContext[safeContext] || 0) + 1;
        
        // 更新错误率
        const gameTimeMinutes = (Date.now() - this._errorStats.gameStartTime) / 60000;
        this._errorStats.errorRate = gameTimeMinutes > 0 
            ? this._errorStats.totalErrors / gameTimeMinutes 
            : this._errorStats.totalErrors;
    }
    
    /**
     * 将错误记录到控制台
     */
    private logErrorToConsole(error: Error, context: string, severity: ErrorSeverity): void {
        if (!error) return;
        
        const severityLabel = ErrorSeverity[severity] || 'UNKNOWN';
        const safeContext = context || 'Unknown';
        
        try {
            switch (severity) {
                case ErrorSeverity.INFO:
                    console.info(`[${severityLabel}][${safeContext}]`, error);
                    break;
                case ErrorSeverity.WARNING:
                    console.warn(`[${severityLabel}][${safeContext}]`, error);
                    break;
                case ErrorSeverity.ERROR:
                case ErrorSeverity.FATAL:
                    console.error(`[${severityLabel}][${safeContext}]`, error);
                    break;
                default:
                    console.log(`[${severityLabel}][${safeContext}]`, error);
                    break;
            }
        } catch (e) {
            // 确保日志功能本身不会引发错误
            console.error('日志记录过程中出错:', e);
        }
    }
    
    /**
     * 处理致命错误
     */
    private handleFatalError(error: Error, context: string): void {
        console.error('致命错误！游戏即将停止', { error, context });
        
        // 这里可以添加游戏暂停或重启的逻辑
        // 例如，显示错误弹窗，提供重启选项
    }
    
    /**
     * 标记错误已恢复
     * @param errorId 错误ID
     * @param context 恢复上下文
     */
    public markErrorAsRecovered(errorId: string, context: string): void {
        if (!errorId) return;
        
        if (this._errors.has(errorId)) {
            const errorInfo = this._errors.get(errorId);
            if (errorInfo) {
                this._errors.delete(errorId);
                
                // 更新错误历史记录
                const historyItem = this._errorHistory.find(item => item.id === errorId);
                if (historyItem) {
                    historyItem.recovered = true;
                }
                
                // 发送恢复事件
                if (this._eventManager && this._isInitialized) {
                    try {
                        this._eventManager.emit(GameEvent.ERROR_RECOVERED, {
                            errorId,
                            context: context || 'Unknown',
                            timestamp: Date.now()
                        });
                    } catch (e) {
                        console.error('发送错误恢复事件失败:', e);
                    }
                }
                
                console.log(`[ErrorManager] 错误已恢复: ${errorId} (${context || 'Unknown'})`);
            }
        }
    }
    
    /**
     * 获取最近的错误
     */
    public getLastError(): Error | null {
        if (!this._lastErrorId || !this._errors.has(this._lastErrorId)) {
            return null;
        }
        const errorInfo = this._errors.get(this._lastErrorId);
        return errorInfo ? errorInfo.error : null;
    }
    
    /**
     * 获取所有未解决的错误
     */
    public getPendingErrors(): Array<{ id: string, error: Error, timestamp: number, severity: ErrorSeverity, context: string }> {
        const result = [];
        try {
            this._errors.forEach((value, key) => {
                result.push({ id: key, ...value });
            });
        } catch (e) {
            console.error('获取未解决错误列表失败:', e);
        }
        return result;
    }
    
    /**
     * 获取错误统计信息
     */
    public getErrorStats(): ErrorStats {
        return { ...this._errorStats };
    }
    
    /**
     * 获取错误历史
     */
    public getErrorHistory(): Array<{
        id: string,
        error: Error,
        timestamp: number,
        severity: ErrorSeverity,
        context: string,
        recovered: boolean
    }> {
        return [...this._errorHistory];
    }
    
    /**
     * 清除所有错误记录（用于重置或测试）
     */
    public clearAllErrors(): void {
        try {
            this._errors.clear();
            this._errorHistory = [];
            this._errorCount = 0;
            this._lastErrorId = '';
            this._throttledErrors.clear();
            
            // 重置统计信息，但保留开始时间
            const gameStartTime = this._errorStats.gameStartTime;
            this._errorStats = {
                totalErrors: 0,
                byType: {},
                bySeverity: {},
                byContext: {},
                errorRate: 0,
                gameStartTime
            };
            
            console.log('[ErrorManager] 所有错误记录已清除');
        } catch (e) {
            console.error('清除错误记录失败:', e);
        }
    }
    
    /**
     * 记录信息级别消息
     */
    public logInfo(message: string, context: string): string {
        if (!message) return null;
        return this.handleError(new Error(message), context || 'Unknown', ErrorSeverity.INFO);
    }
    
    /**
     * 记录警告级别消息
     */
    public logWarning(message: string, context: string): string {
        if (!message) return null;
        return this.handleError(new Error(message), context || 'Unknown', ErrorSeverity.WARNING);
    }
    
    /**
     * 记录错误级别消息
     */
    public logError(error: Error | string, context: string): string {
        if (!error) return null;
        const errorObj = typeof error === 'string' ? new Error(error) : error;
        return this.handleError(errorObj, context || 'Unknown', ErrorSeverity.ERROR);
    }
    
    /**
     * 记录致命错误
     */
    public logFatal(error: Error | string, context: string): string {
        if (!error) return null;
        const errorObj = typeof error === 'string' ? new Error(error) : error;
        return this.handleError(errorObj, context || 'Unknown', ErrorSeverity.FATAL);
    }
    
    /**
     * 为特定类型的错误添加恢复策略
     * @param errorType 错误类型名称
     * @param recoveryFn 恢复函数
     */
    public addRecoveryStrategy(errorType: string, recoveryFn: (error: Error, context: string) => boolean): void {
        if (!errorType) return;
        
        this._autoRecoverableErrors.add(errorType);
        
        // 记录日志
        if (this._logManager) {
            try {
                this._logManager.writeToLog(
                    `为 ${errorType} 添加恢复策略`,
                    'INFO',
                    'ErrorManager.addRecoveryStrategy'
                );
            } catch (e) {
                console.error('记录恢复策略日志失败:', e);
            }
        }
    }
    
    /**
     * 生成错误分析报告
     * 用于开发调试
     */
    public generateErrorReport(): string {
        try {
            const analyzer = ErrorAnalyzer.getInstance();
            return analyzer.generateErrorReport();
        } catch (e) {
            console.error('生成错误报告失败:', e);
            return `错误报告生成失败: ${e.message || '未知错误'}`;
        }
    }
    
    /**
     * 获取错误热点列表
     */
    public getErrorHotspots(): Array<{
        errorSignature: string,
        count: number,
        pattern: string
    }> {
        try {
            const analyzer = ErrorAnalyzer.getInstance();
            return analyzer.getHotspotErrors();
        } catch (e) {
            console.error('获取错误热点列表失败:', e);
            return [];
        }
    }
}