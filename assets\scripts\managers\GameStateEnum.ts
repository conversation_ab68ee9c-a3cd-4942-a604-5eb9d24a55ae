/**
 * 游戏状态枚举
 */
export enum GameState {
    /** 初始化 */
    INIT = 'init',
    /** 游戏开始 */
    START = 'start',
    /** 游戏中 */
    PLAYING = 'playing',
    /** 游戏暂停 */
    PAUSED = 'paused',
    /** 游戏结束 */
    GAME_OVER = 'gameOver',

}
export enum GameEvent {
    // 游戏状态事件
    GAME_INITIALIZED = 'game:initialized',
    GAME_START = 'game:start',
    GAME_PAUSE = 'game:pause',
    GAME_RESUME = 'game:resume',
    GAME_OVER = 'game:over',
    GAME_SREOC = 'game:sreoc'
}
export interface GameEventMap {
    [GameEvent.GAME_INITIALIZED]: { timestamp: number };
    [GameEvent.GAME_START]: { timestamp: number };
    [GameEvent.GAME_OVER]: { reason: string };
    [GameEvent.GAME_PAUSE]: { damage: number; targetId: string };
    [GameEvent.GAME_RESUME]: { characterId: string; position: { x: number; y: number } };
    [GameEvent.GAME_RESUME]: { characterId: string; position: { x: number; y: number } };
    [GameEvent.GAME_SREOC]: { score: number };

}