import { _decorator, Component, Node, Label, Button } from 'cc';
import { ErrorManager } from '../../managers/ErrorManager';
import { GameEvent } from '../../events/GameEvents';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 错误对话框UI组件
 * 用于在游戏中显示错误信息
 */
@ccclass('ErrorDialogUI')
export class ErrorDialogUI extends Component {
    @property(Label)
    private titleLabel: Label = null;

    @property(Label)
    private messageLabel: Label = null;

    @property(Button)
    private closeButton: Button = null;

    @property(Button)
    private retryButton: Button = null;

    @property(Node)
    private errorPanel: Node = null;

    private _currentErrorId: string = '';
    private _currentErrorContext: string = '';
    private _eventManager: EventManager<any> = null;

    /**
     * 组件初始化
     */
    onLoad() {
        // 默认隐藏错误面板
        this.hideErrorPanel();

        // 注册按钮事件
        if (this.closeButton) {
            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClicked, this);
        }

        if (this.retryButton) {
            this.retryButton.node.on(Button.EventType.CLICK, this.onRetryButtonClicked, this);
        }

        // 获取事件管理器
        this._eventManager = EventManager.getInstance();

        // 订阅错误事件
        this._eventManager.on(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
    }

    onDestroy() {
        // 取消事件监听
        if (this._eventManager) {
            this._eventManager.off(GameEvent.ERROR_OCCURRED, this.onErrorOccurred, this);
        }

        // 取消按钮事件
        if (this.closeButton) {
            this.closeButton.node.off(Button.EventType.CLICK, this.onCloseButtonClicked, this);
        }

        if (this.retryButton) {
            this.retryButton.node.off(Button.EventType.CLICK, this.onRetryButtonClicked, this);
        }
    }

    /**
     * 发生错误时的回调
     * @param event 错误事件数据
     */
    private onErrorOccurred(event: { error: Error, context: string, isFatal: boolean, timestamp: number, diagnosis?: any }) {
        const { error, context, isFatal, timestamp, diagnosis } = event;

        // 只处理致命错误或需要用户交互的错误
        if (!isFatal && !this.shouldShowError(diagnosis)) {
            return;
        }

        // 设置错误ID（使用时间戳作为简单标识）
        this._currentErrorId = `error-${timestamp}`;
        this._currentErrorContext = context;

        // 显示错误信息
        this.showError(error, isFatal, diagnosis);
    }

    /**
     * 判断是否应该显示错误对话框
     * @param diagnosis 错误诊断
     * @returns 是否显示
     */
    private shouldShowError(diagnosis?: any): boolean {
        // 可根据诊断结果决定是否弹窗
        if (diagnosis && diagnosis.priority >= 8) return true;
        // 默认只显示致命或高优先级错误
        return false;
    }

    /**
     * 显示错误信息
     * @param error 错误对象
     * @param isFatal 是否为致命错误
     * @param diagnosis 错误诊断
     */
    private showError(error: Error, isFatal: boolean, diagnosis?: any) {
        if (!this.errorPanel || !this.titleLabel || !this.messageLabel) {
            console.error('ErrorDialogUI: 缺少必要UI组件');
            return;
        }

        // 设置标题
        this.titleLabel.string = isFatal ? '致命错误' : '警告';

        // 设置消息
        let msg = `${error.message}\n\n${this._currentErrorContext ? `位置: ${this._currentErrorContext}` : ''}`;
        if (diagnosis && diagnosis.suggestedFix) {
            msg += `\n建议: ${diagnosis.suggestedFix}`;
        }
        this.messageLabel.string = msg;

        // 显示/隐藏重试按钮
        if (this.retryButton) {
            this.retryButton.node.active = !isFatal; // 致命错误不提供重试选项
        }

        // 显示面板
        this.showErrorPanel();
    }

    /**
     * 显示错误面板
     */
    private showErrorPanel() {
        if (this.errorPanel) {
            this.errorPanel.active = true;
        }
    }

    /**
     * 隐藏错误面板
     */
    private hideErrorPanel() {
        if (this.errorPanel) {
            this.errorPanel.active = false;
        }
    }

    /**
     * 关闭按钮点击事件
     */
    private onCloseButtonClicked() {
        this.hideErrorPanel();

        // 如果有错误ID，标记为已恢复
        if (this._currentErrorId) {
            ErrorManager.getInstance().markErrorAsRecovered(
                this._currentErrorId,
                `User acknowledged: ${this._currentErrorContext}`
            );
        }
    }

    /**
     * 重试按钮点击事件
     */
    private onRetryButtonClicked() {
        this.hideErrorPanel();

        // 如果有错误ID，标记为已恢复并尝试重试
        if (this._currentErrorId) {
            ErrorManager.getInstance().markErrorAsRecovered(
                this._currentErrorId,
                `User retry: ${this._currentErrorContext}`
            );

            // 这里可以发送重试事件，或直接调用相关功能
            console.log('用户尝试重试操作');
        }
    }
}
