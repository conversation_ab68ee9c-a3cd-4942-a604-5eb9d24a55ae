import { IState } from '../../core/StateMachine';
import { WeaponsBase, WeaponState } from './WeaponsBase';

/**
 * 武器空闲状态
 */
export class WeaponIdleState implements IState {
    private owner: WeaponsBase;

    constructor(owner: WeaponsBase) {
        this.owner = owner;
    }

    get name(): string {
        return WeaponState.IDLE;
    }

    onEnter(_prevState?: IState): void {
        // 进入空闲状态的逻辑
        console.log('武器进入空闲状态');
    }

    onUpdate(_dt: number): void {
        // 空闲状态更新逻辑
        // 检查是否可以攻击
        const hasTargets = this.owner.hasTargets();
        const canAttack = this.owner.canAttack();
        console.log(`武器空闲状态更新: 有目标=${hasTargets}, 可以攻击=${canAttack}`);

        if (hasTargets && canAttack) {
            console.log(`武器从空闲状态切换到攻击状态`);
            this.owner.changeState(WeaponState.ATTACKING);
        }
    }

    onExit(_nextState?: IState): void {
        // 退出空闲状态的逻辑
    }

    canTransitionTo(_targetState: IState): boolean {
        // 空闲状态可以过渡到任何状态
        return true;
    }
}

/**
 * 武器攻击状态
 */
export class WeaponAttackingState implements IState {
    protected owner: WeaponsBase;
    private attackDuration: number = 0;
    private attackFinished: boolean = false;

    constructor(owner: WeaponsBase) {
        this.owner = owner;
    }

    get name(): string {
        return WeaponState.ATTACKING;
    }

    onEnter(_prevState?: IState): void {
        // 进入攻击状态的逻辑
        console.log('武器进入攻击状态');
        this.attackDuration = 0;
        this.attackFinished = false;

        // 执行攻击
        this.owner.performAttack();
    }

    onUpdate(dt: number): void {
        // 攻击状态更新逻辑
        this.attackDuration += dt;

        // 攻击完成后或冷却完成，返回空闲状态
        if (this.attackFinished || this.attackDuration > this.owner.attackDuration) {
            this.owner.changeState(WeaponState.IDLE);
        }
    }

    onExit(_nextState?: IState): void {
        // 退出攻击状态的逻辑
        this.owner.resetCooldown();
    }

    canTransitionTo(targetState: IState): boolean {
        // 攻击状态可以过渡到空闲状态和死亡状态
        return targetState.name === WeaponState.IDLE || targetState.name === WeaponState.DEAD;
    }
}

/**
 * 武器死亡状态
 */
export class WeaponDeadState implements IState {
    private owner: WeaponsBase;

    constructor(owner: WeaponsBase) {
        this.owner = owner;
    }

    get name(): string {
        return WeaponState.DEAD;
    }

    onEnter(_prevState?: IState): void {
        // 进入死亡状态的逻辑
        console.log('武器进入死亡状态');
        this.owner.onDeath();
    }

    onUpdate(_dt: number): void {
        // 死亡状态不做任何更新
    }

    onExit(_nextState?: IState): void {
        // 退出死亡状态的逻辑
    }

    canTransitionTo(_targetState: IState): boolean {
        // 死亡状态不能过渡到任何其他状态
        return false;
    }
}
